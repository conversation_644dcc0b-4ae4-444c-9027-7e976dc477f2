<?php
/**
 * Simple test file to check PHP setup
 */

echo "<h1>ProVendor Test Page</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";

// Test database connection
try {
    require_once __DIR__ . '/config/database.php';
    $db = Database::getInstance();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test if tables exist
    $tables = DB::select("SHOW TABLES");
    echo "<p>Database tables found: " . count($tables) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test language system
try {
    require_once __DIR__ . '/includes/language.php';
    Language::init();
    echo "<p style='color: green;'>✓ Language system loaded</p>";
    echo "<p>Current language: " . Language::getCurrentLang() . "</p>";
    echo "<p>Test translation: " . __('home') . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Language system failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test security system
try {
    require_once __DIR__ . '/includes/security.php';
    Security::startSecureSession();
    echo "<p style='color: green;'>✓ Security system loaded</p>";
    echo "<p>Session ID: " . session_id() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Security system failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>Go to Homepage</a></p>";
echo "<p><a href='login.php'>Go to Login</a></p>";
echo "<p><a href='register.php'>Go to Register</a></p>";
echo "<p><a href='admin/dashboard.php'>Go to Admin Dashboard</a></p>";
?>
