<?php
/**
 * Logout Handler
 */

require_once __DIR__ . '/includes/security.php';
require_once __DIR__ . '/includes/language.php';
require_once __DIR__ . '/includes/auth.php';

Security::startSecureSession();
Language::init();

// Logout user
Auth::logout();

// Set success message
session_start();
$_SESSION['flash_message'] = [
    'type' => 'success',
    'message' => __('logout_success')
];

// Redirect to home page
header('Location: /');
exit;
