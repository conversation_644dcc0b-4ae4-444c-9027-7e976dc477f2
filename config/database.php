<?php
/**
 * Database Configuration
 * Secure database connection with prepared statements
 */

class Database {
    private static $instance = null;
    private $connection;
    
    // Database configuration
    private $host = 'localhost';
    private $dbname = 'provendor';
    private $username = 'root';
    private $password = 'MMMmmm@123';
    private $charset = 'utf8mb4';
    
    private function __construct() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset} COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            die("Database connection failed. Please try again later.");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // Prevent cloning
    private function __clone() {}
    
    // Prevent unserialization
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * Database Helper Class
 * Provides secure database operations with prepared statements
 */
class DB {
    private static $db;
    
    private static function getDB() {
        if (!self::$db) {
            self::$db = Database::getInstance()->getConnection();
        }
        return self::$db;
    }
    
    /**
     * Execute a SELECT query with prepared statements
     */
    public static function select($query, $params = []) {
        try {
            $stmt = self::getDB()->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Database select error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Execute a SELECT query and return single row
     */
    public static function selectOne($query, $params = []) {
        try {
            $stmt = self::getDB()->prepare($query);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Database selectOne error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Execute INSERT query and return last insert ID
     */
    public static function insert($query, $params = []) {
        try {
            $stmt = self::getDB()->prepare($query);
            $result = $stmt->execute($params);
            return $result ? self::getDB()->lastInsertId() : false;
        } catch (PDOException $e) {
            error_log("Database insert error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Execute UPDATE query and return affected rows
     */
    public static function update($query, $params = []) {
        try {
            $stmt = self::getDB()->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Database update error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Execute DELETE query and return affected rows
     */
    public static function delete($query, $params = []) {
        try {
            $stmt = self::getDB()->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Database delete error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Begin transaction
     */
    public static function beginTransaction() {
        return self::getDB()->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public static function commit() {
        return self::getDB()->commit();
    }
    
    /**
     * Rollback transaction
     */
    public static function rollback() {
        return self::getDB()->rollback();
    }
    
    /**
     * Check if we're in a transaction
     */
    public static function inTransaction() {
        return self::getDB()->inTransaction();
    }
}
