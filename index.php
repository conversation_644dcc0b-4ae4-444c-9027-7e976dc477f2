<?php
/**
 * Homepage - Multi-Vendor E-commerce Platform
 */

$pageTitle = 'Home'; // Default title
require_once __DIR__ . '/includes/header.php';
require_once __DIR__ . '/config/database.php';

// Get featured products, categories, and deals
$featuredProducts = DB::select("
    SELECT p.*, pi.image_url, u.username as vendor_name, vp.business_name
    FROM products p 
    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
    LEFT JOIN users u ON p.vendor_id = u.id
    LEFT JOIN vendor_profiles vp ON u.id = vp.user_id
    WHERE p.status = 'approved' AND p.is_featured = 1 
    ORDER BY p.created_at DESC 
    LIMIT 8
");

$categories = DB::select("
    SELECT c.*, COUNT(p.id) as product_count
    FROM categories c
    LEFT JOIN products p ON c.id = p.category_id AND p.status = 'approved'
    WHERE c.is_active = 1 AND c.parent_id IS NULL
    GROUP BY c.id
    ORDER BY c.sort_order, c.name_" . current_lang() . "
    LIMIT 8
");

$saleProducts = DB::select("
    SELECT p.*, pi.image_url, u.username as vendor_name, vp.business_name
    FROM products p 
    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
    LEFT JOIN users u ON p.vendor_id = u.id
    LEFT JOIN vendor_profiles vp ON u.id = vp.user_id
    WHERE p.status = 'approved' AND p.sale_price IS NOT NULL AND p.sale_price < p.price
    ORDER BY ((p.price - p.sale_price) / p.price) DESC
    LIMIT 4
");
?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="hero-content">
                    <p class="text-primary fw-bold mb-2" style="font-size: 0.9rem; text-transform: uppercase; letter-spacing: 1px;">
                        <?= __('up_to_70_off_black_friday') ?>
                    </p>
                    <h1 class="hero-title">
                        <?= __('trendy') ?> <span class="text-primary"><?= __('fashion') ?></span><br>
                        <?= __('collection') ?>
                    </h1>
                    <p class="hero-subtitle">
                        <?= __('discover_amazing_products') ?>
                    </p>
                    <a href="/products.php" class="hero-cta">
                        <?= __('buy_now') ?>
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image text-center">
                    <img src="/assets/images/hero-woman.svg" alt="Fashion Shopping" class="img-fluid" style="max-height: 450px;">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Promotional Cards Section -->
<section class="promo-section py-5 bg-light-custom">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="promo-card">
                    <div class="promo-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <h5>30% Sale</h5>
                    <p>Gadget Store</p>
                    <small class="text-primary fw-bold">Buy Now</small>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="promo-card">
                    <div class="promo-icon">
                        <i class="fas fa-percent"></i>
                    </div>
                    <h5>Save 30%</h5>
                    <p>Bundle Package</p>
                    <small class="text-primary fw-bold">Save 30%</small>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="promo-card">
                    <div class="promo-icon">
                        <i class="fas fa-fire"></i>
                    </div>
                    <h5>30% Sale</h5>
                    <p>Valentine's Offer</p>
                    <small class="text-primary fw-bold">Buy Now</small>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="promo-card">
                    <div class="promo-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h5>New Arrival</h5>
                    <p>Black Chair</p>
                    <small class="text-primary fw-bold">Purchase</small>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="categories-section py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="section-title"><?= __('browse_categories') ?></h2>
                <p class="section-subtitle"><?= __('discover_amazing_products') ?></p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($categories as $category): ?>
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <a href="/category/<?= Security::escape($category['slug']) ?>" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-th-large"></i>
                    </div>
                    <h5 class="fw-bold"><?= Security::escape($category['name_' . current_lang()]) ?></h5>
                    <p class="text-muted mb-0"><?= $category['product_count'] ?> <?= __('products') ?></p>
                </a>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Sale Banner -->
<section class="sale-banner py-5 bg-primary text-white">
    <div class="container">
        <div class="row">
            <?php foreach ($saleProducts as $index => $product): ?>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="sale-item text-center">
                    <div class="sale-badge mb-3">
                        <span class="badge bg-warning text-dark fs-6">
                            <?= round((($product['price'] - $product['sale_price']) / $product['price']) * 100) ?>% <?= __('sale') ?>
                        </span>
                    </div>
                    <img src="<?= Security::escape($product['image_url'] ?: '/assets/images/placeholder.jpg') ?>" 
                         alt="<?= Security::escape($product['name_' . current_lang()]) ?>" 
                         class="img-fluid mb-3" style="height: 150px; object-fit: cover;">
                    <h6 class="fw-bold"><?= Security::escape($product['name_' . current_lang()]) ?></h6>
                    <div class="price">
                        <span class="price-sale fs-5 fw-bold">
                            <?= Language::formatCurrency($product['sale_price']) ?>
                        </span>
                        <span class="price-old ms-2">
                            <?= Language::formatCurrency($product['price']) ?>
                        </span>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Featured Products -->
<section class="featured-products py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="section-title"><?= __('featured_items') ?></h2>
                <p class="section-subtitle"><?= __('handpicked_products') ?></p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($featuredProducts as $product): ?>
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card product-card">
                    <!-- Product Badges -->
                    <?php if ($product['sale_price'] && $product['sale_price'] < $product['price']): ?>
                    <span class="product-badge sale"><?= __('sale') ?></span>
                    <?php endif; ?>
                    
                    <?php if ($product['is_featured']): ?>
                    <span class="product-badge featured" style="top: 3rem;"><?= __('featured') ?></span>
                    <?php endif; ?>
                    
                    <!-- Product Actions -->
                    <div class="product-actions">
                        <button class="product-action-btn" title="<?= __('add_to_wishlist') ?>">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="product-action-btn" title="<?= __('quick_view') ?>">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="product-action-btn" title="<?= __('compare') ?>">
                            <i class="fas fa-balance-scale"></i>
                        </button>
                    </div>
                    
                    <!-- Product Image -->
                    <a href="/product/<?= Security::escape($product['slug']) ?>">
                        <img src="<?= Security::escape($product['image_url'] ?: '/assets/images/placeholder.jpg') ?>" 
                             class="card-img-top" 
                             alt="<?= Security::escape($product['name_' . current_lang()]) ?>">
                    </a>
                    
                    <!-- Product Info -->
                    <div class="card-body">
                        <div class="vendor-name mb-2">
                            <small class="text-muted">
                                <i class="fas fa-store me-1"></i>
                                <?= Security::escape($product['business_name'] ?: $product['vendor_name']) ?>
                            </small>
                        </div>
                        
                        <h6 class="card-title">
                            <a href="/product/<?= Security::escape($product['slug']) ?>" class="text-decoration-none text-dark">
                                <?= Security::escape($product['name_' . current_lang()]) ?>
                            </a>
                        </h6>
                        
                        <p class="card-text text-muted small">
                            <?= Security::escape(substr($product['short_description_' . current_lang()], 0, 80)) ?>...
                        </p>
                        
                        <!-- Rating -->
                        <div class="rating mb-2">
                            <div class="stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star text-warning"></i>
                                <?php endfor; ?>
                            </div>
                            <small class="text-muted">(4.5)</small>
                        </div>
                        
                        <!-- Price -->
                        <div class="price-container">
                            <?php if ($product['sale_price'] && $product['sale_price'] < $product['price']): ?>
                            <span class="price-old">
                                <?= Language::formatCurrency($product['price']) ?>
                            </span>
                            <span class="price price-sale">
                                <?= Language::formatCurrency($product['sale_price']) ?>
                            </span>
                            <?php else: ?>
                            <span class="price">
                                <?= Language::formatCurrency($product['price']) ?>
                            </span>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Add to Cart -->
                        <button class="btn btn-primary w-100 add-to-cart" 
                                data-product-id="<?= $product['id'] ?>">
                            <i class="fas fa-shopping-cart me-2"></i>
                            <?= __('add_to_cart') ?>
                        </button>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="/products.php" class="btn btn-outline-primary btn-lg">
                <?= __('view_all_products') ?>
            </a>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="services-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="service-item text-center">
                    <div class="service-icon mb-3">
                        <i class="fas fa-shipping-fast fa-3x text-primary"></i>
                    </div>
                    <h5 class="fw-bold"><?= __('free_shipping') ?></h5>
                    <p class="text-muted"><?= __('free_shipping_desc') ?></p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="service-item text-center">
                    <div class="service-icon mb-3">
                        <i class="fas fa-headset fa-3x text-primary"></i>
                    </div>
                    <h5 class="fw-bold"><?= __('24_7_support') ?></h5>
                    <p class="text-muted"><?= __('support_desc') ?></p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="service-item text-center">
                    <div class="service-icon mb-3">
                        <i class="fas fa-medal fa-3x text-primary"></i>
                    </div>
                    <h5 class="fw-bold"><?= __('best_prices') ?></h5>
                    <p class="text-muted"><?= __('best_prices_desc') ?></p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="service-item text-center">
                    <div class="service-icon mb-3">
                        <i class="fas fa-undo fa-3x text-primary"></i>
                    </div>
                    <h5 class="fw-bold"><?= __('easy_returns') ?></h5>
                    <p class="text-muted"><?= __('easy_returns_desc') ?></p>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Add to Cart functionality
$(document).on('click', '.add-to-cart', function() {
    const productId = $(this).data('product-id');
    const button = $(this);
    const originalText = button.html();
    
    button.html('<span class="spinner"></span> ' + window.ProVendor.translations.loading);
    button.prop('disabled', true);
    
    $.ajax({
        url: '/ajax/add-to-cart.php',
        method: 'POST',
        data: {
            product_id: productId,
            quantity: 1,
            csrf_token: window.ProVendor.csrfToken
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message || '<?= __('product_added_to_cart') ?>');
                // Update cart count if needed
                if (response.cart_count) {
                    $('.cart-count').text(response.cart_count);
                }
            } else {
                toastr.error(response.message || window.ProVendor.translations.error);
            }
        },
        error: function() {
            toastr.error(window.ProVendor.translations.error);
        },
        complete: function() {
            button.html(originalText);
            button.prop('disabled', false);
        }
    });
});
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
