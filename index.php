<?php
/**
 * Homepage - Minago E-commerce Platform
 */

$pageTitle = 'Home'; // Default title
require_once __DIR__ . '/includes/header.php';
require_once __DIR__ . '/config/database.php';

// Get featured products, categories, and deals
$featuredProducts = DB::select("
    SELECT p.*, pi.image_url, u.username as vendor_name, vp.business_name
    FROM products p 
    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
    LEFT JOIN users u ON p.vendor_id = u.id
    LEFT JOIN vendor_profiles vp ON u.id = vp.user_id
    WHERE p.status = 'approved' AND p.is_featured = 1 
    ORDER BY p.created_at DESC 
    LIMIT 8
");

$categories = DB::select("
    SELECT c.*, COUNT(p.id) as product_count
    FROM categories c
    LEFT JOIN products p ON c.id = p.category_id AND p.status = 'approved'
    WHERE c.is_active = 1 AND c.parent_id IS NULL
    GROUP BY c.id
    ORDER BY c.sort_order, c.name_" . current_lang() . "
    LIMIT 8
");

$saleProducts = DB::select("
    SELECT p.*, pi.image_url, u.username as vendor_name, vp.business_name
    FROM products p 
    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
    LEFT JOIN users u ON p.vendor_id = u.id
    LEFT JOIN vendor_profiles vp ON u.id = vp.user_id
    WHERE p.status = 'approved' AND p.sale_price IS NOT NULL AND p.sale_price < p.price
    ORDER BY ((p.price - p.sale_price) / p.price) DESC
    LIMIT 4
");
?>

<!-- Main Layout Container -->
<div class="main-layout">
    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-title">Browse Categories</div>
        <ul class="sidebar-menu">
            <li><a href="/category/fashion">Fashion Collection <span class="count">5</span></a></li>
            <li><a href="/category/electronics">Electronics Store <span class="count">3</span></a></li>
            <li><a href="/category/home">Home Appliances <span class="count">7</span></a></li>
            <li><a href="/category/kitchen">Kitchen Item <span class="count">4</span></a></li>
            <li><a href="/category/furniture">Furniture <span class="count">2</span></a></li>
            <li><a href="/category/food">Food <span class="count">6</span></a></li>
            <li><a href="/category/gadgets">Gadgets <span class="count">8</span></a></li>
            <li><a href="/category/toys">Toys and Games <span class="count">3</span></a></li>
            <li><a href="/category/health">Health & Beauty <span class="count">5</span></a></li>
        </ul>
        <div class="mt-4">
            <a href="/categories" class="text-primary text-decoration-none">View All Categories</a>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Banner -->
        <section class="hero-banner">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <p class="hero-discount">Up to 70% off Black Friday</p>
                        <h1 class="hero-title">
                            TRENDY <span class="highlight">FASHION</span><br>
                            COLLECTION
                        </h1>
                        <a href="/products.php" class="hero-cta">Buy Now</a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image">
                        <img src="/assets/images/hero-woman.svg" alt="Fashion Shopping" class="img-fluid">
                    </div>
                </div>
            </div>
        </section>

        <!-- Promotional Cards Section -->
        <section class="promo-section">
            <div class="row g-3 mx-3 my-4">
                <div class="col-lg-3 col-md-6">
                    <div class="promo-card bg-light">
                        <div class="promo-icon bg-primary">
                            <i class="fas fa-laptop"></i>
                        </div>
                        <h5>30% Sale</h5>
                        <p>Gadget Store</p>
                        <small class="text-primary fw-bold">Buy Now</small>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="promo-card bg-light">
                        <div class="promo-icon bg-warning">
                            <i class="fas fa-gift"></i>
                        </div>
                        <h5>Save 30%</h5>
                        <p>Bundle Package</p>
                        <small class="text-primary fw-bold">Save 30%</small>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="promo-card bg-light">
                        <div class="promo-icon bg-danger">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h5>30% Sale</h5>
                        <p>Valentine's Offer</p>
                        <small class="text-primary fw-bold">Buy Now</small>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="promo-card bg-light">
                        <div class="promo-icon bg-success">
                            <i class="fas fa-chair"></i>
                        </div>
                        <h5>New Arrival</h5>
                        <p>Black Chair</p>
                        <small class="text-primary fw-bold">Purchase</small>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Items Section -->
        <section class="featured-section">
            <div class="mx-3 my-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3 class="fw-bold">Featured Item</h3>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary">←</button>
                        <button class="btn btn-sm btn-outline-secondary">→</button>
                    </div>
                </div>
                
                <div class="product-grid">
                    <?php foreach ($featuredProducts as $product): ?>
                    <div class="product-card">
                        <?php if ($product['sale_price'] && $product['sale_price'] < $product['price']): ?>
                        <div class="product-badge">SALE</div>
                        <?php endif; ?>
                        
                        <div class="product-image">
                            <img src="<?= Security::escape($product['image_url'] ?: '/assets/images/placeholder.svg') ?>" 
                                 alt="<?= Security::escape($product['name_' . current_lang()]) ?>">
                        </div>
                        
                        <div class="product-info">
                            <h6 class="product-title"><?= Security::escape($product['name_' . current_lang()]) ?></h6>
                            
                            <div class="product-rating">
                                <div class="stars">★★★★★</div>
                                <span class="rating-text">(4.5)</span>
                            </div>
                            
                            <div class="product-price">
                                <?php if ($product['sale_price'] && $product['sale_price'] < $product['price']): ?>
                                <span class="price-current"><?= Language::formatCurrency($product['sale_price']) ?></span>
                                <span class="price-old"><?= Language::formatCurrency($product['price']) ?></span>
                                <?php else: ?>
                                <span class="price-current"><?= Language::formatCurrency($product['price']) ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <button class="add-to-cart-btn" data-product-id="<?= $product['id'] ?>">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    </main>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
