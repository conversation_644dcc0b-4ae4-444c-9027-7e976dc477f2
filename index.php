<?php
/**
 * Homepage - Minago E-commerce Platform
 */

$pageTitle = 'Home'; // Default title
require_once __DIR__ . '/includes/header.php';
require_once __DIR__ . '/config/database.php';

// Get featured products, categories, and deals
$featuredProducts = DB::select("
    SELECT p.*, pi.image_url, u.username as vendor_name, vp.business_name
    FROM products p 
    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
    LEFT JOIN users u ON p.vendor_id = u.id
    LEFT JOIN vendor_profiles vp ON u.id = vp.user_id
    WHERE p.status = 'approved' AND p.is_featured = 1 
    ORDER BY p.created_at DESC 
    LIMIT 8
");

$categories = DB::select("
    SELECT c.*, COUNT(p.id) as product_count
    FROM categories c
    LEFT JOIN products p ON c.id = p.category_id AND p.status = 'approved'
    WHERE c.is_active = 1 AND c.parent_id IS NULL
    GROUP BY c.id
    ORDER BY c.sort_order, c.name_" . current_lang() . "
    LIMIT 8
");

$saleProducts = DB::select("
    SELECT p.*, pi.image_url, u.username as vendor_name, vp.business_name
    FROM products p 
    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
    LEFT JOIN users u ON p.vendor_id = u.id
    LEFT JOIN vendor_profiles vp ON u.id = vp.user_id
    WHERE p.status = 'approved' AND p.sale_price IS NOT NULL AND p.sale_price < p.price
    ORDER BY ((p.price - p.sale_price) / p.price) DESC
    LIMIT 4
");
?>

<!-- Modern Homepage Layout -->
<div class="main-layout">
    <!-- Hero Section -->
    <section class="hero-section bg-gradient-to-r from-blue-50 to-indigo-100 py-20">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <span class="inline-block bg-primary text-white px-4 py-2 rounded-full text-sm font-semibold mb-4">
                            🔥 Limited Time Offer
                        </span>
                        <h1 class="text-5xl font-bold text-gray-900 mb-6 leading-tight">
                            Discover Amazing
                            <span class="text-primary">Products</span>
                            <br>from Top Vendors
                        </h1>
                        <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                            Shop from thousands of verified vendors and get the best deals on quality products with fast shipping and excellent customer service.
                        </p>
                        <div class="flex flex-wrap gap-4">
                            <a href="/products.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-shopping-bag me-2"></i>
                                Shop Now
                            </a>
                            <a href="/vendors.php" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-store me-2"></i>
                                Browse Vendors
                            </a>
                        </div>
                        <div class="mt-8 flex items-center gap-6 text-sm text-gray-500">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-truck text-green-500"></i>
                                Free Shipping
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-shield-alt text-blue-500"></i>
                                Secure Payment
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-undo text-orange-500"></i>
                                Easy Returns
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image text-center">
                        <img src="/assets/images/hero-ecommerce.svg" alt="E-commerce Shopping" class="img-fluid" style="max-height: 500px;">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="py-16 bg-white">
        <div class="container">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Shop by Category</h2>
                <p class="text-lg text-gray-600">Discover products across all categories</p>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                <?php foreach ($categories as $category): ?>
                <a href="/category/<?= Security::escape($category['slug']) ?>"
                   class="group bg-gray-50 rounded-2xl p-6 text-center hover:bg-primary hover:text-white transition-all duration-300 hover:transform hover:scale-105">
                    <div class="w-16 h-16 bg-primary bg-opacity-10 group-hover:bg-white group-hover:bg-opacity-20 rounded-full flex items-center justify-content-center mx-auto mb-4">
                        <i class="fas fa-th-large text-2xl text-primary group-hover:text-white"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 group-hover:text-white mb-2">
                        <?= Security::escape($category['name_' . current_lang()]) ?>
                    </h3>
                    <p class="text-sm text-gray-500 group-hover:text-gray-200">
                        <?= $category['product_count'] ?> products
                    </p>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-gray-50">
        <div class="container">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-content-center mx-auto mb-4">
                        <i class="fas fa-shipping-fast text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Free Shipping</h3>
                    <p class="text-gray-600">Free shipping on orders over $50. Fast and reliable delivery.</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-content-center mx-auto mb-4">
                        <i class="fas fa-shield-alt text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Secure Payment</h3>
                    <p class="text-gray-600">Your payment information is safe and secure with us.</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-content-center mx-auto mb-4">
                        <i class="fas fa-undo text-2xl text-orange-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Easy Returns</h3>
                    <p class="text-gray-600">30-day return policy. No questions asked.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="py-16 bg-white">
        <div class="container">
            <div class="flex justify-between items-center mb-12">
                <div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">Featured Products</h2>
                    <p class="text-lg text-gray-600">Handpicked products from our top vendors</p>
                </div>
                <a href="/products.php" class="btn btn-outline-primary">
                    View All Products
                    <i class="fas fa-arrow-right ms-2"></i>
                </a>
            </div>

            <div class="product-grid">
                <?php foreach ($featuredProducts as $product): ?>
                <div class="product-card">
                    <!-- Product Badge -->
                    <?php if ($product['sale_price'] && $product['sale_price'] < $product['price']): ?>
                    <div class="product-badge">
                        <?= round((($product['price'] - $product['sale_price']) / $product['price']) * 100) ?>% OFF
                    </div>
                    <?php endif; ?>

                    <!-- Product Actions -->
                    <div class="product-actions">
                        <button class="product-action-btn" title="Add to Wishlist">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="product-action-btn" title="Quick View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="product-action-btn" title="Compare">
                            <i class="fas fa-balance-scale"></i>
                        </button>
                    </div>

                    <!-- Product Image -->
                    <div class="product-image">
                        <a href="/product/<?= Security::escape($product['slug']) ?>">
                            <img src="<?= Security::escape($product['image_url'] ?: '/assets/images/placeholder.svg') ?>"
                                 alt="<?= Security::escape($product['name_' . current_lang()]) ?>">
                        </a>
                    </div>

                    <!-- Product Info -->
                    <div class="product-info">
                        <div class="product-vendor">
                            <?= Security::escape($product['business_name'] ?: $product['vendor_name']) ?>
                        </div>

                        <h3 class="product-title">
                            <a href="/product/<?= Security::escape($product['slug']) ?>">
                                <?= Security::escape($product['name_' . current_lang()]) ?>
                            </a>
                        </h3>

                        <div class="product-rating">
                            <div class="rating-stars">
                                <span class="star">★</span>
                                <span class="star">★</span>
                                <span class="star">★</span>
                                <span class="star">★</span>
                                <span class="star empty">★</span>
                            </div>
                            <span class="rating-count">(4.2)</span>
                        </div>

                        <div class="product-price">
                            <?php if ($product['sale_price'] && $product['sale_price'] < $product['price']): ?>
                            <span class="price-current"><?= Language::formatCurrency($product['sale_price']) ?></span>
                            <span class="price-original"><?= Language::formatCurrency($product['price']) ?></span>
                            <span class="price-discount">
                                <?= round((($product['price'] - $product['sale_price']) / $product['price']) * 100) ?>% OFF
                            </span>
                            <?php else: ?>
                            <span class="price-current"><?= Language::formatCurrency($product['price']) ?></span>
                            <?php endif; ?>
                        </div>

                        <button class="add-to-cart-btn" data-product-id="<?= $product['id'] ?>">
                            <i class="fas fa-shopping-cart me-2"></i>
                            Add to Cart
                        </button>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
