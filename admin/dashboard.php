<?php
/**
 * Admin Dashboard
 */

require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/language.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../config/database.php';

Security::requireAdmin();
Language::init();

// Get dashboard statistics
$stats = [
    'pending_users' => DB::selectOne("SELECT COUNT(*) as count FROM users WHERE status = 'pending'")['count'] ?? 0,
    'total_users' => DB::selectOne("SELECT COUNT(*) as count FROM users WHERE role != 'admin'")['count'] ?? 0,
    'total_vendors' => DB::selectOne("SELECT COUNT(*) as count FROM users WHERE role = 'vendor' AND status = 'approved'")['count'] ?? 0,
    'total_products' => DB::selectOne("SELECT COUNT(*) as count FROM products WHERE status = 'approved'")['count'] ?? 0,
    'pending_products' => DB::selectOne("SELECT COUNT(*) as count FROM products WHERE status = 'pending'")['count'] ?? 0,
    'total_orders' => DB::selectOne("SELECT COUNT(*) as count FROM orders")['count'] ?? 0,
];

// Get recent pending users
$pendingUsers = DB::select("
    SELECT u.*, vp.business_name 
    FROM users u 
    LEFT JOIN vendor_profiles vp ON u.id = vp.user_id 
    WHERE u.status = 'pending' 
    ORDER BY u.created_at DESC 
    LIMIT 10
");

// Handle user approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!Security::verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $_SESSION['flash_message'] = ['type' => 'error', 'message' => __('invalid_request')];
    } else {
        $userId = (int)($_POST['user_id'] ?? 0);
        $action = $_POST['action'];
        
        if ($userId > 0 && in_array($action, ['approve', 'reject', 'suspend'])) {
            $status = match($action) {
                'approve' => 'approved',
                'reject' => 'rejected',
                'suspend' => 'suspended'
            };
            
            $updated = DB::update("UPDATE users SET status = ? WHERE id = ?", [$status, $userId]);
            
            if ($updated) {
                Security::logActivity($_SESSION['user_id'], 'user_' . $action, 'user', $userId);
                $_SESSION['flash_message'] = [
                    'type' => 'success', 
                    'message' => __('user_' . $action . '_success')
                ];
            } else {
                $_SESSION['flash_message'] = ['type' => 'error', 'message' => __('operation_failed')];
            }
        }
        
        header('Location: /admin/dashboard.php');
        exit;
    }
}

$pageTitle = __('admin_panel');
require_once __DIR__ . '/../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i><?= __('admin_panel') ?>
                    </h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="/admin/dashboard.php" class="list-group-item list-group-item-action active">
                        <i class="fas fa-tachometer-alt me-2"></i><?= __('dashboard') ?>
                    </a>
                    <a href="/admin/users.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i><?= __('user_management') ?>
                        <?php if ($stats['pending_users'] > 0): ?>
                        <span class="badge bg-warning text-dark"><?= $stats['pending_users'] ?></span>
                        <?php endif; ?>
                    </a>
                    <a href="/admin/vendors.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-store me-2"></i><?= __('vendor_management') ?>
                    </a>
                    <a href="/admin/products.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-box me-2"></i><?= __('product_management') ?>
                        <?php if ($stats['pending_products'] > 0): ?>
                        <span class="badge bg-warning text-dark"><?= $stats['pending_products'] ?></span>
                        <?php endif; ?>
                    </a>
                    <a href="/admin/orders.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i><?= __('order_management') ?>
                    </a>
                    <a href="/admin/categories.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-tags me-2"></i><?= __('category_management') ?>
                    </a>
                    <a href="/admin/settings.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-cog me-2"></i><?= __('system_settings') ?>
                    </a>
                    <a href="/admin/reports.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i><?= __('reports') ?>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-md-8">
            <!-- Welcome Message -->
            <div class="row mb-4">
                <div class="col-12">
                    <h1 class="h3 mb-0"><?= __('welcome_admin') ?>, <?= Security::escape($_SESSION['first_name']) ?>!</h1>
                    <p class="text-muted"><?= __('admin_dashboard_description') ?></p>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?= $stats['total_users'] ?></h4>
                                    <p class="mb-0"><?= __('total_users') ?></p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?= $stats['total_vendors'] ?></h4>
                                    <p class="mb-0"><?= __('active_vendors') ?></p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-store fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?= $stats['total_products'] ?></h4>
                                    <p class="mb-0"><?= __('total_products') ?></p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-box fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?= $stats['total_orders'] ?></h4>
                                    <p class="mb-0"><?= __('total_orders') ?></p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-shopping-cart fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Pending Approvals -->
            <?php if (!empty($pendingUsers)): ?>
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i><?= __('pending_approvals') ?>
                    </h5>
                    <span class="badge bg-warning text-dark"><?= count($pendingUsers) ?></span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?= __('user') ?></th>
                                    <th><?= __('role') ?></th>
                                    <th><?= __('business_name') ?></th>
                                    <th><?= __('registered') ?></th>
                                    <th><?= __('actions') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pendingUsers as $user): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?= Security::escape($user['first_name'] . ' ' . $user['last_name']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= Security::escape($user['email']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $user['role'] === 'vendor' ? 'primary' : 'secondary' ?>">
                                            <?= __($user['role']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?= $user['business_name'] ? Security::escape($user['business_name']) : '-' ?>
                                    </td>
                                    <td>
                                        <?= Language::formatDate($user['created_at']) ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <form method="POST" class="d-inline" onsubmit="return confirm('<?= __('confirm_approve') ?>')">
                                                <?= Security::csrfField() ?>
                                                <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                                <input type="hidden" name="action" value="approve">
                                                <button type="submit" class="btn btn-success btn-sm">
                                                    <i class="fas fa-check"></i> <?= __('approve') ?>
                                                </button>
                                            </form>
                                            
                                            <form method="POST" class="d-inline" onsubmit="return confirm('<?= __('confirm_reject') ?>')">
                                                <?= Security::csrfField() ?>
                                                <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                                <input type="hidden" name="action" value="reject">
                                                <button type="submit" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-times"></i> <?= __('reject') ?>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <?php if (count($pendingUsers) >= 10): ?>
                    <div class="text-center mt-3">
                        <a href="/admin/users.php?status=pending" class="btn btn-outline-primary">
                            <?= __('view_all_pending') ?>
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Quick Actions -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i><?= __('quick_actions') ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="/admin/users.php?status=pending" class="btn btn-outline-primary">
                                    <i class="fas fa-user-check me-2"></i><?= __('review_pending_users') ?>
                                    <?php if ($stats['pending_users'] > 0): ?>
                                    <span class="badge bg-warning text-dark"><?= $stats['pending_users'] ?></span>
                                    <?php endif; ?>
                                </a>
                                
                                <a href="/admin/products.php?status=pending" class="btn btn-outline-info">
                                    <i class="fas fa-box-open me-2"></i><?= __('review_pending_products') ?>
                                    <?php if ($stats['pending_products'] > 0): ?>
                                    <span class="badge bg-warning text-dark"><?= $stats['pending_products'] ?></span>
                                    <?php endif; ?>
                                </a>
                                
                                <a href="/admin/categories.php" class="btn btn-outline-success">
                                    <i class="fas fa-plus me-2"></i><?= __('add_category') ?>
                                </a>
                                
                                <a href="/admin/settings.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-cog me-2"></i><?= __('system_settings') ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i><?= __('recent_activity') ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="activity-feed">
                                <!-- Activity items would be loaded here -->
                                <div class="activity-item d-flex mb-3">
                                    <div class="activity-icon bg-success text-white rounded-circle me-3">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div>
                                        <strong><?= __('new_user_registered') ?></strong>
                                        <br>
                                        <small class="text-muted"><?= __('2_minutes_ago') ?></small>
                                    </div>
                                </div>
                                
                                <div class="activity-item d-flex mb-3">
                                    <div class="activity-icon bg-info text-white rounded-circle me-3">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <div>
                                        <strong><?= __('new_order_placed') ?></strong>
                                        <br>
                                        <small class="text-muted"><?= __('5_minutes_ago') ?></small>
                                    </div>
                                </div>
                                
                                <div class="activity-item d-flex">
                                    <div class="activity-icon bg-warning text-dark rounded-circle me-3">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    <div>
                                        <strong><?= __('product_submitted_review') ?></strong>
                                        <br>
                                        <small class="text-muted"><?= __('10_minutes_ago') ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh pending count every 30 seconds
setInterval(function() {
    $.get('/ajax/admin-stats.php', function(data) {
        if (data.pending_users !== undefined) {
            $('.badge').each(function() {
                if ($(this).closest('a').attr('href').includes('pending')) {
                    $(this).text(data.pending_users);
                    if (data.pending_users == 0) {
                        $(this).hide();
                    } else {
                        $(this).show();
                    }
                }
            });
        }
    });
}, 30000);
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>
