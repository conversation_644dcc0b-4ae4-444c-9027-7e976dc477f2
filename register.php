<?php
/**
 * Registration Page with Admin Approval
 */

require_once __DIR__ . '/includes/security.php';
require_once __DIR__ . '/includes/language.php';
require_once __DIR__ . '/includes/auth.php';

Security::startSecureSession();
Language::init();

// Redirect if already logged in
if (Security::isLoggedIn()) {
    header('Location: /');
    exit;
}

$error = '';
$success = '';
$role = Security::sanitizeInput($_GET['role'] ?? 'customer');

// Validate role
if (!in_array($role, ['customer', 'vendor'])) {
    $role = 'customer';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!Security::verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = __('invalid_request');
    } else {
        $data = [
            'username' => Security::sanitizeInput($_POST['username'] ?? ''),
            'email' => Security::sanitizeInput($_POST['email'] ?? ''),
            'password' => $_POST['password'] ?? '',
            'confirm_password' => $_POST['confirm_password'] ?? '',
            'first_name' => Security::sanitizeInput($_POST['first_name'] ?? ''),
            'last_name' => Security::sanitizeInput($_POST['last_name'] ?? ''),
            'phone' => Security::sanitizeInput($_POST['phone'] ?? ''),
            'role' => Security::sanitizeInput($_POST['role'] ?? 'customer')
        ];
        
        // Vendor-specific fields
        if ($data['role'] === 'vendor') {
            $data['business_name'] = Security::sanitizeInput($_POST['business_name'] ?? '');
            $data['business_description'] = Security::sanitizeInput($_POST['business_description'] ?? '');
            $data['business_address'] = Security::sanitizeInput($_POST['business_address'] ?? '');
            $data['business_phone'] = Security::sanitizeInput($_POST['business_phone'] ?? '');
            $data['business_email'] = Security::sanitizeInput($_POST['business_email'] ?? '');
        }
        
        $result = Auth::register($data);
        
        if ($result['success']) {
            $success = __('registration_success');
            // Clear form data
            $_POST = [];
        } else {
            $error = implode('<br>', $result['errors']);
        }
    }
}

$pageTitle = __('register');
$breadcrumbs = [
    ['title' => __('register')]
];

require_once __DIR__ . '/includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card shadow">
                <div class="card-body p-5">
                    <!-- Header -->
                    <div class="text-center mb-4">
                        <h2 class="fw-bold text-primary">
                            <?= $role === 'vendor' ? __('vendor_application') : __('register') ?>
                        </h2>
                        <p class="text-muted">
                            <?= $role === 'vendor' ? __('join_our_vendor_network') : __('create_your_account') ?>
                        </p>
                    </div>
                    
                    <!-- Role Switcher -->
                    <div class="text-center mb-4">
                        <div class="btn-group" role="group">
                            <a href="/register.php?role=customer" 
                               class="btn <?= $role === 'customer' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                <i class="fas fa-user me-2"></i><?= __('customer') ?>
                            </a>
                            <a href="/register.php?role=vendor" 
                               class="btn <?= $role === 'vendor' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                <i class="fas fa-store me-2"></i><?= __('vendor') ?>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Error/Success Messages -->
                    <?php if ($error): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= $error ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= Security::escape($success) ?>
                        <div class="mt-2">
                            <a href="/login.php" class="btn btn-sm btn-success">
                                <?= __('login_now') ?>
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!$success): ?>
                    <!-- Registration Form -->
                    <form method="POST" id="registerForm">
                        <?= Security::csrfField() ?>
                        <input type="hidden" name="role" value="<?= Security::escape($role) ?>">
                        
                        <!-- Personal Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="fw-bold text-primary mb-3">
                                    <i class="fas fa-user me-2"></i><?= __('personal_information') ?>
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- First Name -->
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label"><?= __('first_name') ?> *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?= Security::escape($_POST['first_name'] ?? '') ?>" 
                                       placeholder="<?= __('first_name') ?>" required>
                            </div>
                            
                            <!-- Last Name -->
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label"><?= __('last_name') ?> *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?= Security::escape($_POST['last_name'] ?? '') ?>" 
                                       placeholder="<?= __('last_name') ?>" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Username -->
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label"><?= __('username') ?> *</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?= Security::escape($_POST['username'] ?? '') ?>" 
                                       placeholder="<?= __('username') ?>" required>
                                <div class="form-text"><?= __('username_help') ?></div>
                            </div>
                            
                            <!-- Phone -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label"><?= __('phone') ?></label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?= Security::escape($_POST['phone'] ?? '') ?>" 
                                       placeholder="<?= __('phone') ?>">
                            </div>
                        </div>
                        
                        <!-- Email -->
                        <div class="mb-3">
                            <label for="email" class="form-label"><?= __('email') ?> *</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?= Security::escape($_POST['email'] ?? '') ?>" 
                                   placeholder="<?= __('email') ?>" required>
                        </div>
                        
                        <div class="row">
                            <!-- Password -->
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label"><?= __('password') ?> *</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="<?= __('password') ?>" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text"><?= __('password_requirements') ?></div>
                            </div>
                            
                            <!-- Confirm Password -->
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label"><?= __('confirm_password') ?> *</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       placeholder="<?= __('confirm_password') ?>" required>
                            </div>
                        </div>
                        
                        <?php if ($role === 'vendor'): ?>
                        <!-- Business Information -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h5 class="fw-bold text-primary mb-3">
                                    <i class="fas fa-store me-2"></i><?= __('business_information') ?>
                                </h5>
                            </div>
                        </div>
                        
                        <!-- Business Name -->
                        <div class="mb-3">
                            <label for="business_name" class="form-label"><?= __('business_name') ?> *</label>
                            <input type="text" class="form-control" id="business_name" name="business_name" 
                                   value="<?= Security::escape($_POST['business_name'] ?? '') ?>" 
                                   placeholder="<?= __('business_name') ?>" required>
                        </div>
                        
                        <!-- Business Description -->
                        <div class="mb-3">
                            <label for="business_description" class="form-label"><?= __('business_description') ?></label>
                            <textarea class="form-control" id="business_description" name="business_description" 
                                      rows="3" placeholder="<?= __('business_description') ?>"><?= Security::escape($_POST['business_description'] ?? '') ?></textarea>
                        </div>
                        
                        <div class="row">
                            <!-- Business Phone -->
                            <div class="col-md-6 mb-3">
                                <label for="business_phone" class="form-label"><?= __('business_phone') ?></label>
                                <input type="tel" class="form-control" id="business_phone" name="business_phone" 
                                       value="<?= Security::escape($_POST['business_phone'] ?? '') ?>" 
                                       placeholder="<?= __('business_phone') ?>">
                            </div>
                            
                            <!-- Business Email -->
                            <div class="col-md-6 mb-3">
                                <label for="business_email" class="form-label"><?= __('business_email') ?></label>
                                <input type="email" class="form-control" id="business_email" name="business_email" 
                                       value="<?= Security::escape($_POST['business_email'] ?? '') ?>" 
                                       placeholder="<?= __('business_email') ?>">
                            </div>
                        </div>
                        
                        <!-- Business Address -->
                        <div class="mb-3">
                            <label for="business_address" class="form-label"><?= __('business_address') ?></label>
                            <textarea class="form-control" id="business_address" name="business_address" 
                                      rows="2" placeholder="<?= __('business_address') ?>"><?= Security::escape($_POST['business_address'] ?? '') ?></textarea>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Terms and Conditions -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms">
                                    <?= __('i_agree_to') ?>
                                    <a href="/terms.php" target="_blank" class="text-decoration-none">
                                        <?= __('terms_of_service') ?>
                                    </a>
                                    <?= __('and') ?>
                                    <a href="/privacy.php" target="_blank" class="text-decoration-none">
                                        <?= __('privacy_policy') ?>
                                    </a>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Submit Button -->
                        <button type="submit" class="btn btn-primary w-100 mb-3" id="registerBtn">
                            <i class="fas fa-user-plus me-2"></i>
                            <?= $role === 'vendor' ? __('submit_application') : __('register') ?>
                        </button>
                    </form>
                    <?php endif; ?>
                    
                    <!-- Login Link -->
                    <div class="text-center mt-4">
                        <p class="mb-0">
                            <?= __('already_have_account') ?>
                            <a href="/login.php" class="text-decoration-none fw-bold">
                                <?= __('login') ?>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Toggle password visibility
    $('#togglePassword').click(function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Form validation
    $('#registerForm').on('submit', function(e) {
        const password = $('#password').val();
        const confirmPassword = $('#confirm_password').val();
        
        if (password !== confirmPassword) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: window.ProVendor.translations.error,
                text: '<?= __('passwords_not_match') ?>'
            });
            return false;
        }
        
        if (password.length < 8) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: window.ProVendor.translations.error,
                text: '<?= __('password_too_short') ?>'
            });
            return false;
        }
        
        // Show loading state
        const btn = $('#registerBtn');
        const originalText = btn.html();
        btn.html('<span class="spinner"></span> ' + window.ProVendor.translations.loading);
        btn.prop('disabled', true);
    });
    
    // Auto-fill business email with personal email
    $('#email').on('blur', function() {
        const businessEmail = $('#business_email');
        if (businessEmail.length && !businessEmail.val()) {
            businessEmail.val($(this).val());
        }
    });
});
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
