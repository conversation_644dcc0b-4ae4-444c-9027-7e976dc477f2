<?php
/**
 * Security Functions
 * CSRF Protection, Input Validation, Session Management
 */

require_once 'config/database.php';

class Security {
    
    /**
     * Generate CSRF Token
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        $token = bin2hex(random_bytes(32));
        $sessionId = session_id();
        $userId = $_SESSION['user_id'] ?? null;
        $expiresAt = date('Y-m-d H:i:s', time() + 3600); // 1 hour
        
        // Store token in database
        $query = "INSERT INTO csrf_tokens (token, user_id, session_id, expires_at) VALUES (?, ?, ?, ?)";
        DB::insert($query, [$token, $userId, $sessionId, $expiresAt]);
        
        // Clean expired tokens
        self::cleanExpiredTokens();
        
        return $token;
    }
    
    /**
     * Verify CSRF Token
     */
    public static function verifyCSRFToken($token) {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        if (empty($token)) {
            return false;
        }
        
        $sessionId = session_id();
        $userId = $_SESSION['user_id'] ?? null;
        
        // Check token in database
        $query = "SELECT id FROM csrf_tokens WHERE token = ? AND session_id = ? AND expires_at > NOW() AND used = FALSE";
        $params = [$token, $sessionId];
        
        if ($userId) {
            $query .= " AND (user_id = ? OR user_id IS NULL)";
            $params[] = $userId;
        } else {
            $query .= " AND user_id IS NULL";
        }
        
        $result = DB::selectOne($query, $params);
        
        if ($result) {
            // Mark token as used
            DB::update("UPDATE csrf_tokens SET used = TRUE WHERE id = ?", [$result['id']]);
            return true;
        }
        
        return false;
    }
    
    /**
     * Clean expired CSRF tokens
     */
    private static function cleanExpiredTokens() {
        DB::delete("DELETE FROM csrf_tokens WHERE expires_at < NOW() OR used = TRUE");
    }
    
    /**
     * Generate CSRF hidden input field
     */
    public static function csrfField() {
        $token = self::generateCSRFToken();
        return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
    }
    
    /**
     * Sanitize input data
     */
    public static function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate email
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validate password strength
     */
    public static function validatePassword($password) {
        // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
        return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/', $password);
    }
    
    /**
     * Hash password
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Verify password
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Generate secure random string
     */
    public static function generateRandomString($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
    
    /**
     * Rate limiting for login attempts
     */
    public static function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 900) {
        $query = "SELECT COUNT(*) as attempts FROM activity_logs 
                  WHERE (ip_address = ? OR user_id = (SELECT id FROM users WHERE email = ? OR username = ?))
                  AND action = 'failed_login' 
                  AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)";
        
        $result = DB::selectOne($query, [$identifier, $identifier, $identifier, $timeWindow]);
        
        return ($result['attempts'] ?? 0) < $maxAttempts;
    }
    
    /**
     * Log security event
     */
    public static function logActivity($userId, $action, $entityType = null, $entityId = null, $details = null) {
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $query = "INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, details) 
                  VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $detailsJson = $details ? json_encode($details) : null;
        
        DB::insert($query, [$userId, $action, $entityType, $entityId, $ipAddress, $userAgent, $detailsJson]);
    }
    
    /**
     * Check if user is logged in
     */
    public static function isLoggedIn() {
        if (!isset($_SESSION)) {
            session_start();
        }
        return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
    }
    
    /**
     * Check if user has specific role
     */
    public static function hasRole($role) {
        if (!self::isLoggedIn()) {
            return false;
        }
        return $_SESSION['user_role'] === $role;
    }
    
    /**
     * Check if user is admin
     */
    public static function isAdmin() {
        return self::hasRole('admin');
    }
    
    /**
     * Check if user is vendor
     */
    public static function isVendor() {
        return self::hasRole('vendor');
    }
    
    /**
     * Require login
     */
    public static function requireLogin() {
        if (!self::isLoggedIn()) {
            header('Location: /login.php');
            exit;
        }
    }
    
    /**
     * Require admin role
     */
    public static function requireAdmin() {
        self::requireLogin();
        if (!self::isAdmin()) {
            header('Location: /unauthorized.php');
            exit;
        }
    }
    
    /**
     * Require vendor role
     */
    public static function requireVendor() {
        self::requireLogin();
        if (!self::isVendor() && !self::isAdmin()) {
            header('Location: /unauthorized.php');
            exit;
        }
    }
    
    /**
     * Escape output for HTML
     */
    public static function escape($data) {
        return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Generate secure session
     */
    public static function startSecureSession() {
        if (!isset($_SESSION)) {
            // Configure secure session settings
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
            ini_set('session.use_strict_mode', 1);
            
            session_start();
            
            // Regenerate session ID periodically
            if (!isset($_SESSION['created'])) {
                $_SESSION['created'] = time();
            } else if (time() - $_SESSION['created'] > 1800) {
                session_regenerate_id(true);
                $_SESSION['created'] = time();
            }
        }
    }
}
