<?php
/**
 * <PERSON>er Template
 * Responsive header with multi-language support
 */

require_once __DIR__ . '/security.php';
require_once __DIR__ . '/language.php';
require_once __DIR__ . '/auth.php';

Security::startSecureSession();
Language::init();

$currentUser = Auth::getCurrentUser();
$cartCount = 0; // TODO: Implement cart count
?>
<!DOCTYPE html>
<html lang="<?= current_lang() ?>" dir="<?= text_direction() ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? Security::escape($pageTitle) . ' - ' : '' ?>ProVendor</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php if (is_rtl()): ?>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php endif; ?>
    <link href="/assets/css/style.css" rel="stylesheet">

    <!-- Toast notifications -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Meta tags -->
    <meta name="description" content="<?= __('site_description') ?>">
    <meta name="keywords" content="e-commerce, multi-vendor, online shopping">
    <meta name="author" content="ProVendor">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
</head>
<body class="<?= is_rtl() ? 'rtl' : 'ltr' ?>">

<!-- Top Bar -->
<div class="top-bar bg-primary text-white py-2">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <i class="fas fa-phone me-2"></i>
                    <span class="me-3">****** 567 8900</span>
                    <i class="fas fa-envelope me-2"></i>
                    <span><EMAIL></span>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <div class="d-flex align-items-center justify-content-end">
                    <!-- Language Switcher -->
                    <?= Language::getLanguageSwitcher() ?>
                    
                    <!-- Social Links -->
                    <div class="social-links ms-3">
                        <a href="#" class="text-white me-2"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-white me-2"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-2"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Header -->
<header class="main-header bg-white shadow-sm">
    <div class="container">
        <div class="row align-items-center py-3">
            <!-- Logo -->
            <div class="col-lg-3 col-md-4 col-6">
                <a href="/" class="logo d-flex align-items-center text-decoration-none">
                    <img src="/assets/images/logo.png" alt="ProVendor" class="logo-img me-2" style="height: 40px;">
                    <span class="logo-text fw-bold text-primary fs-4">ProVendor</span>
                </a>
            </div>
            
            <!-- Search Bar -->
            <div class="col-lg-5 col-md-4 d-none d-md-block">
                <form class="search-form" action="/search.php" method="GET">
                    <div class="input-group">
                        <input type="text" class="form-control border-end-0" name="q" 
                               placeholder="<?= __('search') ?>..." value="<?= Security::escape($_GET['q'] ?? '') ?>">
                        <button class="btn btn-outline-secondary border-start-0" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Header Actions -->
            <div class="col-lg-4 col-md-4 col-6">
                <div class="header-actions d-flex align-items-center justify-content-end">
                    
                    <!-- User Account -->
                    <?php if ($currentUser): ?>
                    <div class="dropdown me-3">
                        <a href="#" class="btn btn-link text-dark text-decoration-none dropdown-toggle" 
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-1"></i>
                            <span class="d-none d-lg-inline"><?= Security::escape($currentUser['first_name']) ?></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile.php">
                                <i class="fas fa-user me-2"></i><?= __('profile') ?>
                            </a></li>
                            <li><a class="dropdown-item" href="/orders.php">
                                <i class="fas fa-shopping-bag me-2"></i><?= __('orders') ?>
                            </a></li>
                            <?php if ($currentUser['role'] === 'vendor'): ?>
                            <li><a class="dropdown-item" href="/vendor/dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i><?= __('vendor_dashboard') ?>
                            </a></li>
                            <?php endif; ?>
                            <?php if ($currentUser['role'] === 'admin'): ?>
                            <li><a class="dropdown-item" href="/admin/dashboard.php">
                                <i class="fas fa-cog me-2"></i><?= __('admin_panel') ?>
                            </a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i><?= __('logout') ?>
                            </a></li>
                        </ul>
                    </div>
                    <?php else: ?>
                    <a href="/login.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        <span class="d-none d-lg-inline"><?= __('login') ?></span>
                    </a>
                    <?php endif; ?>
                    
                    <!-- Wishlist -->
                    <a href="/wishlist.php" class="btn btn-link text-dark me-2 position-relative">
                        <i class="fas fa-heart"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            0
                        </span>
                    </a>
                    
                    <!-- Shopping Cart -->
                    <a href="/cart.php" class="btn btn-primary position-relative">
                        <i class="fas fa-shopping-cart me-1"></i>
                        <span class="d-none d-lg-inline"><?= __('cart') ?></span>
                        <?php if ($cartCount > 0): ?>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning text-dark">
                            <?= $cartCount ?>
                        </span>
                        <?php endif; ?>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Mobile Search -->
        <div class="row d-md-none">
            <div class="col-12 pb-3">
                <form class="search-form" action="/search.php" method="GET">
                    <div class="input-group">
                        <input type="text" class="form-control" name="q" 
                               placeholder="<?= __('search') ?>..." value="<?= Security::escape($_GET['q'] ?? '') ?>">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</header>

<!-- Navigation -->
<nav class="main-nav">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-pills justify-content-center">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i><?= __('home') ?>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <i class="fas fa-th-large me-1"></i><?= __('categories') ?>
                        </a>
                        <ul class="dropdown-menu">
                            <!-- Categories will be loaded dynamically -->
                            <li><a class="dropdown-item" href="/category/electronics"><?= __('electronics') ?></a></li>
                            <li><a class="dropdown-item" href="/category/fashion"><?= __('clothing') ?></a></li>
                            <li><a class="dropdown-item" href="/category/home"><?= __('home_appliances') ?></a></li>
                            <li><a class="dropdown-item" href="/category/books"><?= __('books') ?></a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/vendors.php">
                            <i class="fas fa-store me-1"></i><?= __('vendors') ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/deals.php">
                            <i class="fas fa-tags me-1"></i><?= __('on_sale') ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/new-arrivals.php">
                            <i class="fas fa-star me-1"></i><?= __('new_arrivals') ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/contact.php">
                            <i class="fas fa-envelope me-1"></i><?= __('contact') ?>
                        </a>
                    </li>
                    <?php if (!$currentUser): ?>
                    <li class="nav-item">
                        <a class="nav-link text-primary fw-bold" href="/register.php?role=vendor">
                            <i class="fas fa-plus me-1"></i><?= __('become_vendor') ?>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>
</nav>

<!-- Breadcrumb (if needed) -->
<?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
<nav aria-label="breadcrumb" class="bg-light">
    <div class="container">
        <ol class="breadcrumb py-2 mb-0">
            <li class="breadcrumb-item"><a href="/"><?= __('home') ?></a></li>
            <?php foreach ($breadcrumbs as $crumb): ?>
                <?php if (isset($crumb['url'])): ?>
                    <li class="breadcrumb-item"><a href="<?= Security::escape($crumb['url']) ?>"><?= Security::escape($crumb['title']) ?></a></li>
                <?php else: ?>
                    <li class="breadcrumb-item active" aria-current="page"><?= Security::escape($crumb['title']) ?></li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </div>
</nav>
<?php endif; ?>

<!-- Main Content Start -->
