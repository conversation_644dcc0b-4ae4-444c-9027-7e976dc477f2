<?php
/**
 * <PERSON>er Template
 * Responsive header with multi-language support
 */

require_once __DIR__ . '/security.php';
require_once __DIR__ . '/language.php';
require_once __DIR__ . '/auth.php';

Security::startSecureSession();
Language::init();

$currentUser = Auth::getCurrentUser();
$cartCount = 0; // TODO: Implement cart count
?>
<!DOCTYPE html>
<html lang="<?= current_lang() ?>" dir="<?= text_direction() ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? Security::escape($pageTitle) . ' - ' : '' ?>ProVendor</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php if (is_rtl()): ?>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php endif; ?>
    <link href="/assets/css/style.css" rel="stylesheet">

    <!-- Toast notifications -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Meta tags -->
    <meta name="description" content="<?= __('site_description') ?>">
    <meta name="keywords" content="e-commerce, multi-vendor, online shopping">
    <meta name="author" content="ProVendor">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
</head>
<body class="<?= is_rtl() ? 'rtl' : 'ltr' ?>">

<!-- Header Top - Minago Style -->
<div class="header-top">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <span class="me-4">All Categories</span>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <div class="d-flex align-items-center justify-content-end">
                    <a href="/account" class="text-white text-decoration-none me-3">
                        <i class="fas fa-user me-1"></i>Account
                    </a>
                    <span class="text-white me-3">$39.00</span>
                    <!-- Language Switcher -->
                    <?= Language::getLanguageSwitcher() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Header -->
<header class="main-header">
    <div class="container">
        <div class="row align-items-center py-3">
            <!-- Logo -->
            <div class="col-lg-2 col-md-3 col-6">
                <a href="/" class="logo d-flex align-items-center text-decoration-none">
                    <span class="logo-text fw-bold text-primary fs-4">minago</span>
                </a>
            </div>
            
            <!-- Search Bar -->
            <div class="col-lg-6 col-md-5 d-none d-md-block">
                <form class="search-form" action="/search.php" method="GET">
                    <div class="input-group">
                        <input type="text" class="form-control" name="q"
                               placeholder="Search Products..." value="<?= Security::escape($_GET['q'] ?? '') ?>">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Header Actions -->
            <div class="col-lg-4 col-md-4 col-6">
                <div class="header-actions d-flex align-items-center justify-content-end">
                    <!-- Wishlist -->
                    <a href="/wishlist.php" class="btn btn-link text-dark me-2">
                        <i class="fas fa-heart"></i>
                        <span class="badge bg-primary">0</span>
                    </a>

                    <!-- Cart -->
                    <a href="/cart.php" class="btn btn-link text-dark me-3">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge bg-primary"><?= $cartCount ?></span>
                    </a>
                    
                    <!-- User Account -->
                    <?php if ($currentUser): ?>
                    <div class="dropdown me-3">
                        <a href="#" class="btn btn-link text-dark text-decoration-none dropdown-toggle" 
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-1"></i>
                            <span class="d-none d-lg-inline"><?= Security::escape($currentUser['first_name']) ?></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile.php">
                                <i class="fas fa-user me-2"></i><?= __('profile') ?>
                            </a></li>
                            <li><a class="dropdown-item" href="/orders.php">
                                <i class="fas fa-shopping-bag me-2"></i><?= __('orders') ?>
                            </a></li>
                            <?php if ($currentUser['role'] === 'vendor'): ?>
                            <li><a class="dropdown-item" href="/vendor/dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i><?= __('vendor_dashboard') ?>
                            </a></li>
                            <?php endif; ?>
                            <?php if ($currentUser['role'] === 'admin'): ?>
                            <li><a class="dropdown-item" href="/admin/dashboard.php">
                                <i class="fas fa-cog me-2"></i><?= __('admin_panel') ?>
                            </a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i><?= __('logout') ?>
                            </a></li>
                        </ul>
                    </div>
                    <?php else: ?>
                    <a href="/login.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        <span class="d-none d-lg-inline"><?= __('login') ?></span>
                    </a>
                    <?php endif; ?>
                    
                    <!-- Wishlist -->
                    <a href="/wishlist.php" class="btn btn-link text-dark me-2 position-relative">
                        <i class="fas fa-heart"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            0
                        </span>
                    </a>
                    
                    <!-- Shopping Cart -->
                    <a href="/cart.php" class="btn btn-primary position-relative">
                        <i class="fas fa-shopping-cart me-1"></i>
                        <span class="d-none d-lg-inline"><?= __('cart') ?></span>
                        <?php if ($cartCount > 0): ?>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning text-dark">
                            <?= $cartCount ?>
                        </span>
                        <?php endif; ?>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Mobile Search -->
        <div class="row d-md-none">
            <div class="col-12 pb-3">
                <form class="search-form" action="/search.php" method="GET">
                    <div class="input-group">
                        <input type="text" class="form-control" name="q" 
                               placeholder="<?= __('search') ?>..." value="<?= Security::escape($_GET['q'] ?? '') ?>">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</header>

<!-- Navigation -->
<nav class="main-nav">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-pills justify-content-start">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/products.php">Products</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">Categories</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/category/electronics">Electronics</a></li>
                            <li><a class="dropdown-item" href="/category/fashion">Fashion</a></li>
                            <li><a class="dropdown-item" href="/category/home">Home & Garden</a></li>
                            <li><a class="dropdown-item" href="/category/books">Books</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/pages.php">Pages</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/campaign.php">Campaign</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/blog.php">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/vendors.php">Vendors</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/track-order.php">Track Order</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/contact.php">Contact Us</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<!-- Breadcrumb (if needed) -->
<?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
<nav aria-label="breadcrumb" class="bg-light">
    <div class="container">
        <ol class="breadcrumb py-2 mb-0">
            <li class="breadcrumb-item"><a href="/"><?= __('home') ?></a></li>
            <?php foreach ($breadcrumbs as $crumb): ?>
                <?php if (isset($crumb['url'])): ?>
                    <li class="breadcrumb-item"><a href="<?= Security::escape($crumb['url']) ?>"><?= Security::escape($crumb['title']) ?></a></li>
                <?php else: ?>
                    <li class="breadcrumb-item active" aria-current="page"><?= Security::escape($crumb['title']) ?></li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </div>
</nav>
<?php endif; ?>

<!-- Main Content Start -->
