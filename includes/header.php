<?php
/**
 * <PERSON>er Template
 * Responsive header with multi-language support
 */

require_once __DIR__ . '/security.php';
require_once __DIR__ . '/language.php';
require_once __DIR__ . '/auth.php';

Security::startSecureSession();
Language::init();

$currentUser = Auth::getCurrentUser();
$cartCount = 0; // TODO: Implement cart count
?>
<!DOCTYPE html>
<html lang="<?= current_lang() ?>" dir="<?= text_direction() ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? Security::escape($pageTitle) . ' - ' : '' ?>ProVendor</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php if (is_rtl()): ?>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php endif; ?>
    <link href="assets/css/style.css" rel="stylesheet">

    <!-- Toast notifications -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Meta tags -->
    <meta name="description" content="<?= __('site_description') ?>">
    <meta name="keywords" content="e-commerce, multi-vendor, online shopping">
    <meta name="author" content="ProVendor">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
</head>
<body class="<?= is_rtl() ? 'rtl' : 'ltr' ?>">

<!-- Header Top -->
<div class="header-top">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center gap-4">
                <span class="text-sm">
                    <i class="fas fa-phone me-2"></i>
                    +****************
                </span>
                <span class="text-sm">
                    <i class="fas fa-envelope me-2"></i>
                    <EMAIL>
                </span>
            </div>
            <div class="d-flex align-items-center gap-4">
                <span class="text-sm">Free shipping on orders over $50</span>
                <!-- Language Switcher -->
                <?= Language::getLanguageSwitcher() ?>
            </div>
        </div>
    </div>
</div>

<!-- Main Header -->
<header class="main-header">
    <div class="container">
        <div class="d-flex align-items-center justify-content-between">
            <!-- Logo -->
            <div class="flex-shrink-0">
                <a href="/" class="logo">
                    ProVendor
                </a>
            </div>

            <!-- Search Bar -->
            <div class="flex-grow-1 mx-8 d-none d-lg-block">
                <form class="search-form" action="/search.php" method="GET">
                    <input type="text" class="form-control" name="q"
                           placeholder="Search for products..."
                           value="<?= Security::escape($_GET['q'] ?? '') ?>">
                    <button class="btn" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>

            <!-- Header Actions -->
            <div class="flex-shrink-0">
                <div class="header-actions">
                    <!-- Wishlist -->
                    <a href="/wishlist.php" class="header-action-btn">
                        <i class="fas fa-heart"></i>
                        <span class="d-none d-md-inline">Wishlist</span>
                        <span class="badge">0</span>
                    </a>

                    <!-- Cart -->
                    <a href="/cart.php" class="header-action-btn">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="d-none d-md-inline">Cart</span>
                        <span class="badge"><?= $cartCount ?></span>
                    </a>
                    
                    <!-- User Account -->
                    <?php if ($currentUser): ?>
                    <div class="user-menu">
                        <a href="#" class="user-menu-btn dropdown-toggle"
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user"></i>
                            <span class="d-none d-lg-inline"><?= Security::escape($currentUser['first_name']) ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/profile.php">
                                <i class="fas fa-user me-2"></i><?= __('profile') ?>
                            </a></li>
                            <li><a class="dropdown-item" href="/orders.php">
                                <i class="fas fa-shopping-bag me-2"></i><?= __('orders') ?>
                            </a></li>
                            <?php if ($currentUser['role'] === 'vendor'): ?>
                            <li><a class="dropdown-item" href="/vendor/dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i><?= __('vendor_dashboard') ?>
                            </a></li>
                            <?php endif; ?>
                            <?php if ($currentUser['role'] === 'admin'): ?>
                            <li><a class="dropdown-item" href="/admin/dashboard.php">
                                <i class="fas fa-cog me-2"></i><?= __('admin_panel') ?>
                            </a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i><?= __('logout') ?>
                            </a></li>
                        </ul>
                    </div>
                    <?php else: ?>
                    <a href="/login.php" class="btn btn-outline-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        <span class="d-none d-lg-inline ms-2"><?= __('login') ?></span>
                    </a>
                    <?php endif; ?>
                    
                    <!-- Wishlist -->
                    <a href="/wishlist.php" class="btn btn-link text-dark me-2 position-relative">
                        <i class="fas fa-heart"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            0
                        </span>
                    </a>
                    
                    <!-- Shopping Cart -->
                    <a href="/cart.php" class="btn btn-primary position-relative">
                        <i class="fas fa-shopping-cart me-1"></i>
                        <span class="d-none d-lg-inline"><?= __('cart') ?></span>
                        <?php if ($cartCount > 0): ?>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning text-dark">
                            <?= $cartCount ?>
                        </span>
                        <?php endif; ?>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Mobile Search -->
        <div class="row d-md-none">
            <div class="col-12 pb-3">
                <form class="search-form" action="/search.php" method="GET">
                    <div class="input-group">
                        <input type="text" class="form-control" name="q" 
                               placeholder="<?= __('search') ?>..." value="<?= Security::escape($_GET['q'] ?? '') ?>">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</header>

<!-- Navigation -->
<nav class="main-nav">
    <div class="nav-container">
        <ul class="nav-pills">
            <li class="nav-item">
                <a class="nav-link active" href="/">
                    <i class="fas fa-home"></i>
                    Home
                </a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                    <i class="fas fa-th-large"></i>
                    Categories
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/category/electronics">
                        <i class="fas fa-laptop me-2"></i>Electronics
                    </a></li>
                    <li><a class="dropdown-item" href="/category/fashion">
                        <i class="fas fa-tshirt me-2"></i>Fashion
                    </a></li>
                    <li><a class="dropdown-item" href="/category/home">
                        <i class="fas fa-home me-2"></i>Home & Garden
                    </a></li>
                    <li><a class="dropdown-item" href="/category/books">
                        <i class="fas fa-book me-2"></i>Books
                    </a></li>
                    <li><a class="dropdown-item" href="/category/sports">
                        <i class="fas fa-dumbbell me-2"></i>Sports
                    </a></li>
                </ul>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/products.php">
                    <i class="fas fa-box"></i>
                    Products
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/deals.php">
                    <i class="fas fa-tags"></i>
                    Deals
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/vendors.php">
                    <i class="fas fa-store"></i>
                    Vendors
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/blog.php">
                    <i class="fas fa-blog"></i>
                    Blog
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/contact.php">
                    <i class="fas fa-envelope"></i>
                    Contact
                </a>
            </li>
        </ul>
    </div>
</nav>

<!-- Breadcrumb (if needed) -->
<?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
<nav aria-label="breadcrumb" class="bg-light">
    <div class="container">
        <ol class="breadcrumb py-2 mb-0">
            <li class="breadcrumb-item"><a href="/"><?= __('home') ?></a></li>
            <?php foreach ($breadcrumbs as $crumb): ?>
                <?php if (isset($crumb['url'])): ?>
                    <li class="breadcrumb-item"><a href="<?= Security::escape($crumb['url']) ?>"><?= Security::escape($crumb['title']) ?></a></li>
                <?php else: ?>
                    <li class="breadcrumb-item active" aria-current="page"><?= Security::escape($crumb['title']) ?></li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </div>
</nav>
<?php endif; ?>

<!-- Main Content Start -->
