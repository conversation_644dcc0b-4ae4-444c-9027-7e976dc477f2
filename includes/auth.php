<?php
/**
 * Authentication and Authorization System
 * Handles user login, registration, and role-based access control
 */

require_once 'config/database.php';
require_once 'includes/security.php';
require_once 'includes/language.php';

class Auth {
    
    /**
     * Register new user with admin approval requirement
     */
    public static function register($data) {
        // Validate input
        $errors = self::validateRegistrationData($data);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Check if user already exists
        if (self::userExists($data['email'], $data['username'])) {
            return ['success' => false, 'errors' => [__('user_already_exists')]];
        }
        
        // Hash password
        $passwordHash = Security::hashPassword($data['password']);
        
        // Generate email verification token
        $verificationToken = Security::generateRandomString();
        
        // Insert user
        $query = "INSERT INTO users (username, email, password_hash, first_name, last_name, phone, role, status, email_verification_token) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?)";
        
        $userId = DB::insert($query, [
            $data['username'],
            $data['email'],
            $passwordHash,
            $data['first_name'],
            $data['last_name'],
            $data['phone'] ?? null,
            $data['role'] ?? 'customer',
            $verificationToken
        ]);
        
        if ($userId) {
            // Log registration activity
            Security::logActivity($userId, 'user_registered', 'user', $userId);
            
            // If registering as vendor, create vendor profile
            if (($data['role'] ?? 'customer') === 'vendor') {
                self::createVendorProfile($userId, $data);
            }
            
            // Send email verification (implement email sending)
            // self::sendVerificationEmail($data['email'], $verificationToken);
            
            return ['success' => true, 'user_id' => $userId];
        }
        
        return ['success' => false, 'errors' => [__('registration_failed')]];
    }
    
    /**
     * Login user with rate limiting and security checks
     */
    public static function login($email, $password, $rememberMe = false) {
        // Check rate limiting
        if (!Security::checkRateLimit($email)) {
            return ['success' => false, 'error' => __('too_many_attempts')];
        }
        
        // Get user
        $user = self::getUserByEmail($email);
        
        if (!$user || !Security::verifyPassword($password, $user['password_hash'])) {
            // Log failed login
            Security::logActivity($user['id'] ?? null, 'failed_login', 'user', $user['id'] ?? null, ['email' => $email]);
            
            // Update failed login attempts
            if ($user) {
                self::updateFailedLoginAttempts($user['id']);
            }
            
            return ['success' => false, 'error' => __('login_failed')];
        }
        
        // Check account status
        if ($user['status'] !== 'approved') {
            $statusMessage = match($user['status']) {
                'pending' => __('account_pending'),
                'suspended' => __('account_suspended'),
                'rejected' => __('account_rejected'),
                default => __('account_inactive')
            };
            return ['success' => false, 'error' => $statusMessage];
        }
        
        // Check if account is locked
        if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
            return ['success' => false, 'error' => __('account_locked')];
        }
        
        // Login successful
        self::createUserSession($user);
        
        // Reset failed login attempts
        self::resetFailedLoginAttempts($user['id']);
        
        // Update last login
        self::updateLastLogin($user['id']);
        
        // Set remember me cookie
        if ($rememberMe) {
            self::setRememberMeCookie($user['id']);
        }
        
        // Log successful login
        Security::logActivity($user['id'], 'successful_login', 'user', $user['id']);
        
        return ['success' => true, 'user' => $user];
    }
    
    /**
     * Logout user
     */
    public static function logout() {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        $userId = $_SESSION['user_id'] ?? null;
        
        // Log logout
        if ($userId) {
            Security::logActivity($userId, 'logout', 'user', $userId);
        }
        
        // Clear session
        session_unset();
        session_destroy();
        
        // Clear remember me cookie
        if (isset($_COOKIE['remember_me'])) {
            setcookie('remember_me', '', time() - 3600, '/');
        }
        
        return true;
    }
    
    /**
     * Validate registration data
     */
    private static function validateRegistrationData($data) {
        $errors = [];
        
        // Required fields
        $required = ['username', 'email', 'password', 'first_name', 'last_name'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                $errors[] = __('field_required') . ': ' . __($field);
            }
        }
        
        // Email validation
        if (!empty($data['email']) && !Security::validateEmail($data['email'])) {
            $errors[] = __('invalid_email');
        }
        
        // Password validation
        if (!empty($data['password']) && !Security::validatePassword($data['password'])) {
            $errors[] = __('password_too_short');
        }
        
        // Confirm password
        if (!empty($data['password']) && !empty($data['confirm_password'])) {
            if ($data['password'] !== $data['confirm_password']) {
                $errors[] = __('passwords_not_match');
            }
        }
        
        // Username validation
        if (!empty($data['username'])) {
            if (strlen($data['username']) < 3 || strlen($data['username']) > 50) {
                $errors[] = __('username_length_invalid');
            }
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
                $errors[] = __('username_format_invalid');
            }
        }
        
        return $errors;
    }
    
    /**
     * Check if user exists
     */
    private static function userExists($email, $username) {
        $query = "SELECT id FROM users WHERE email = ? OR username = ?";
        return DB::selectOne($query, [$email, $username]) !== false;
    }
    
    /**
     * Get user by email
     */
    private static function getUserByEmail($email) {
        $query = "SELECT * FROM users WHERE email = ?";
        return DB::selectOne($query, [$email]);
    }
    
    /**
     * Create user session
     */
    private static function createUserSession($user) {
        Security::startSecureSession();
        
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['first_name'] = $user['first_name'];
        $_SESSION['last_name'] = $user['last_name'];
        $_SESSION['logged_in'] = true;
        $_SESSION['login_time'] = time();
    }
    
    /**
     * Update failed login attempts
     */
    private static function updateFailedLoginAttempts($userId) {
        $query = "UPDATE users SET failed_login_attempts = failed_login_attempts + 1 WHERE id = ?";
        DB::update($query, [$userId]);
        
        // Lock account after 5 failed attempts
        $user = DB::selectOne("SELECT failed_login_attempts FROM users WHERE id = ?", [$userId]);
        if ($user && $user['failed_login_attempts'] >= 5) {
            $lockUntil = date('Y-m-d H:i:s', time() + 1800); // 30 minutes
            DB::update("UPDATE users SET locked_until = ? WHERE id = ?", [$lockUntil, $userId]);
        }
    }
    
    /**
     * Reset failed login attempts
     */
    private static function resetFailedLoginAttempts($userId) {
        $query = "UPDATE users SET failed_login_attempts = 0, locked_until = NULL WHERE id = ?";
        DB::update($query, [$userId]);
    }
    
    /**
     * Update last login
     */
    private static function updateLastLogin($userId) {
        $query = "UPDATE users SET last_login = NOW() WHERE id = ?";
        DB::update($query, [$userId]);
    }
    
    /**
     * Set remember me cookie
     */
    private static function setRememberMeCookie($userId) {
        $token = Security::generateRandomString();
        $expires = time() + (86400 * 30); // 30 days
        
        // Store token in database (you might want to create a remember_tokens table)
        setcookie('remember_me', $token, $expires, '/', '', isset($_SERVER['HTTPS']), true);
    }
    
    /**
     * Create vendor profile
     */
    private static function createVendorProfile($userId, $data) {
        $query = "INSERT INTO vendor_profiles (user_id, business_name, business_description, business_address, business_phone, business_email) 
                  VALUES (?, ?, ?, ?, ?, ?)";
        
        return DB::insert($query, [
            $userId,
            $data['business_name'] ?? '',
            $data['business_description'] ?? '',
            $data['business_address'] ?? '',
            $data['business_phone'] ?? '',
            $data['business_email'] ?? $data['email']
        ]);
    }
    
    /**
     * Get current user
     */
    public static function getCurrentUser() {
        if (!Security::isLoggedIn()) {
            return null;
        }
        
        $userId = $_SESSION['user_id'];
        $query = "SELECT u.*, vp.business_name, vp.business_description 
                  FROM users u 
                  LEFT JOIN vendor_profiles vp ON u.id = vp.user_id 
                  WHERE u.id = ?";
        
        return DB::selectOne($query, [$userId]);
    }
    
    /**
     * Check if current user can access resource
     */
    public static function canAccess($resource, $action = 'view') {
        $user = self::getCurrentUser();
        if (!$user) {
            return false;
        }
        
        // Admin can access everything
        if ($user['role'] === 'admin') {
            return true;
        }
        
        // Define permissions
        $permissions = [
            'vendor' => [
                'products' => ['view', 'create', 'edit', 'delete'],
                'orders' => ['view'],
                'dashboard' => ['view'],
                'profile' => ['view', 'edit']
            ],
            'customer' => [
                'products' => ['view'],
                'cart' => ['view', 'edit'],
                'orders' => ['view', 'create'],
                'profile' => ['view', 'edit']
            ]
        ];
        
        $userPermissions = $permissions[$user['role']] ?? [];
        $resourcePermissions = $userPermissions[$resource] ?? [];
        
        return in_array($action, $resourcePermissions);
    }
}
