<?php
/**
 * Multi-Language Support System
 * Supports Arabic and English with RTL/LTR handling
 */

class Language {
    private static $currentLang = 'en';
    private static $translations = [];
    private static $supportedLanguages = ['en', 'ar'];
    
    /**
     * Initialize language system
     */
    public static function init() {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        // Get language from session, cookie, or default
        if (isset($_GET['lang']) && in_array($_GET['lang'], self::$supportedLanguages)) {
            self::$currentLang = $_GET['lang'];
            $_SESSION['language'] = self::$currentLang;
            setcookie('language', self::$currentLang, time() + (86400 * 30), '/');
        } elseif (isset($_SESSION['language'])) {
            self::$currentLang = $_SESSION['language'];
        } elseif (isset($_COOKIE['language'])) {
            self::$currentLang = $_COOKIE['language'];
        } else {
            // Detect browser language
            $browserLang = self::detectBrowserLanguage();
            self::$currentLang = $browserLang;
        }
        
        // Load translations
        self::loadTranslations();
    }
    
    /**
     * Detect browser language
     */
    private static function detectBrowserLanguage() {
        if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            $langs = explode(',', $_SERVER['HTTP_ACCEPT_LANGUAGE']);
            foreach ($langs as $lang) {
                $lang = substr(trim($lang), 0, 2);
                if (in_array($lang, self::$supportedLanguages)) {
                    return $lang;
                }
            }
        }
        return 'en'; // Default fallback
    }
    
    /**
     * Load translation files
     */
    private static function loadTranslations() {
        $langFile = __DIR__ . "/../lang/" . self::$currentLang . ".php";
        if (file_exists($langFile)) {
            self::$translations = include $langFile;
        }
    }
    
    /**
     * Get translation
     */
    public static function get($key, $params = []) {
        $translation = self::$translations[$key] ?? $key;
        
        // Replace parameters
        if (!empty($params)) {
            foreach ($params as $param => $value) {
                $translation = str_replace(':' . $param, $value, $translation);
            }
        }
        
        return $translation;
    }
    
    /**
     * Get current language
     */
    public static function getCurrentLang() {
        return self::$currentLang;
    }
    
    /**
     * Check if current language is RTL
     */
    public static function isRTL() {
        return self::$currentLang === 'ar';
    }
    
    /**
     * Get opposite direction
     */
    public static function getOppositeDirection() {
        return self::isRTL() ? 'ltr' : 'rtl';
    }
    
    /**
     * Get text direction
     */
    public static function getDirection() {
        return self::isRTL() ? 'rtl' : 'ltr';
    }
    
    /**
     * Get language switcher HTML
     */
    public static function getLanguageSwitcher() {
        $currentUrl = $_SERVER['REQUEST_URI'];
        $separator = strpos($currentUrl, '?') !== false ? '&' : '?';
        
        $html = '<div class="language-switcher">';
        foreach (self::$supportedLanguages as $lang) {
            $active = $lang === self::$currentLang ? 'active' : '';
            $langName = $lang === 'ar' ? 'العربية' : 'English';
            $url = $currentUrl . $separator . 'lang=' . $lang;
            
            $html .= '<a href="' . htmlspecialchars($url) . '" class="lang-link ' . $active . '" data-lang="' . $lang . '">';
            $html .= $langName . '</a>';
        }
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Get supported languages
     */
    public static function getSupportedLanguages() {
        return self::$supportedLanguages;
    }
    
    /**
     * Format number based on language
     */
    public static function formatNumber($number, $decimals = 0) {
        if (self::$currentLang === 'ar') {
            // Arabic number formatting
            return number_format($number, $decimals, '.', ',');
        } else {
            // English number formatting
            return number_format($number, $decimals, '.', ',');
        }
    }
    
    /**
     * Format currency based on language
     */
    public static function formatCurrency($amount, $currency = 'USD') {
        $formatted = self::formatNumber($amount, 2);
        
        if (self::$currentLang === 'ar') {
            return $formatted . ' ' . $currency;
        } else {
            return $currency . ' ' . $formatted;
        }
    }
    
    /**
     * Format date based on language
     */
    public static function formatDate($date, $format = null) {
        if (!$format) {
            $format = self::$currentLang === 'ar' ? 'd/m/Y' : 'm/d/Y';
        }
        
        if (is_string($date)) {
            $date = new DateTime($date);
        }
        
        return $date->format($format);
    }
}

/**
 * Translation helper function
 */
function __($key, $params = []) {
    return Language::get($key, $params);
}

/**
 * Translation helper function with escaping
 */
function _e($key, $params = []) {
    echo htmlspecialchars(Language::get($key, $params), ENT_QUOTES, 'UTF-8');
}

/**
 * Get current language
 */
function current_lang() {
    return Language::getCurrentLang();
}

/**
 * Check if RTL
 */
function is_rtl() {
    return Language::isRTL();
}

/**
 * Get text direction
 */
function text_direction() {
    return Language::getDirection();
}
