<?php
/**
 * CSRF Token Refresh AJAX Handler
 */

header('Content-Type: application/json');

require_once __DIR__ . '/../includes/security.php';

Security::startSecureSession();

// Check if request is AJAX
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

try {
    $token = Security::generateCSRFToken();
    
    echo json_encode([
        'success' => true,
        'token' => $token
    ]);
    
} catch (Exception $e) {
    error_log("CSRF token generation error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
