<?php
/**
 * Add to Cart AJAX Handler
 */

header('Content-Type: application/json');

require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/language.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../config/database.php';

Security::startSecureSession();
Language::init();

// Check if request is AJAX
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => __('invalid_request')]);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => __('method_not_allowed')]);
    exit;
}

// Verify CSRF token
if (!Security::verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    http_response_code(419);
    echo json_encode(['success' => false, 'message' => __('invalid_request')]);
    exit;
}

try {
    $productId = (int)($_POST['product_id'] ?? 0);
    $quantity = (int)($_POST['quantity'] ?? 1);
    
    if ($productId <= 0 || $quantity <= 0) {
        echo json_encode(['success' => false, 'message' => __('invalid_data')]);
        exit;
    }
    
    // Get product details
    $product = DB::selectOne("
        SELECT p.*, u.status as vendor_status 
        FROM products p 
        JOIN users u ON p.vendor_id = u.id 
        WHERE p.id = ? AND p.status = 'approved'
    ", [$productId]);
    
    if (!$product) {
        echo json_encode(['success' => false, 'message' => __('product_not_found')]);
        exit;
    }
    
    // Check if vendor is active
    if ($product['vendor_status'] !== 'approved') {
        echo json_encode(['success' => false, 'message' => __('vendor_not_active')]);
        exit;
    }
    
    // Check stock
    if ($product['stock_quantity'] < $quantity) {
        echo json_encode(['success' => false, 'message' => __('insufficient_stock')]);
        exit;
    }
    
    $userId = $_SESSION['user_id'] ?? null;
    $sessionId = session_id();
    $price = $product['sale_price'] ?: $product['price'];
    
    // Check if item already exists in cart
    $existingItem = null;
    if ($userId) {
        $existingItem = DB::selectOne("
            SELECT * FROM cart_items 
            WHERE user_id = ? AND product_id = ?
        ", [$userId, $productId]);
    } else {
        $existingItem = DB::selectOne("
            SELECT * FROM cart_items 
            WHERE session_id = ? AND product_id = ? AND user_id IS NULL
        ", [$sessionId, $productId]);
    }
    
    if ($existingItem) {
        // Update quantity
        $newQuantity = $existingItem['quantity'] + $quantity;
        
        // Check stock again
        if ($product['stock_quantity'] < $newQuantity) {
            echo json_encode(['success' => false, 'message' => __('insufficient_stock')]);
            exit;
        }
        
        $updated = DB::update("
            UPDATE cart_items 
            SET quantity = ?, price = ?, updated_at = NOW() 
            WHERE id = ?
        ", [$newQuantity, $price, $existingItem['id']]);
        
        if (!$updated) {
            echo json_encode(['success' => false, 'message' => __('failed_to_update_cart')]);
            exit;
        }
    } else {
        // Add new item
        $inserted = DB::insert("
            INSERT INTO cart_items (user_id, session_id, product_id, quantity, price) 
            VALUES (?, ?, ?, ?, ?)
        ", [$userId, $sessionId, $productId, $quantity, $price]);
        
        if (!$inserted) {
            echo json_encode(['success' => false, 'message' => __('failed_to_add_to_cart')]);
            exit;
        }
    }
    
    // Get cart count
    $cartCount = 0;
    if ($userId) {
        $result = DB::selectOne("SELECT SUM(quantity) as count FROM cart_items WHERE user_id = ?", [$userId]);
        $cartCount = $result['count'] ?? 0;
    } else {
        $result = DB::selectOne("SELECT SUM(quantity) as count FROM cart_items WHERE session_id = ? AND user_id IS NULL", [$sessionId]);
        $cartCount = $result['count'] ?? 0;
    }
    
    // Log activity
    if ($userId) {
        Security::logActivity($userId, 'add_to_cart', 'product', $productId, [
            'quantity' => $quantity,
            'price' => $price
        ]);
    }
    
    echo json_encode([
        'success' => true,
        'message' => __('product_added_to_cart'),
        'cart_count' => $cartCount
    ]);
    
} catch (Exception $e) {
    error_log("Add to cart error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => __('server_error')]);
}
