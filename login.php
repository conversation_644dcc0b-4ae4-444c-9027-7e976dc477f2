<?php
/**
 * Login Page
 */

require_once __DIR__ . '/includes/security.php';
require_once __DIR__ . '/includes/language.php';
require_once __DIR__ . '/includes/auth.php';

Security::startSecureSession();
Language::init();

// Redirect if already logged in
if (Security::isLoggedIn()) {
    $redirectUrl = match($_SESSION['user_role']) {
        'admin' => '/admin/dashboard.php',
        'vendor' => '/vendor/dashboard.php',
        default => '/'
    };
    header('Location: ' . $redirectUrl);
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!Security::verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = __('invalid_request');
    } else {
        $email = Security::sanitizeInput($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $rememberMe = isset($_POST['remember_me']);
        
        if (empty($email) || empty($password)) {
            $error = __('field_required');
        } else {
            $result = Auth::login($email, $password, $rememberMe);
            
            if ($result['success']) {
                $_SESSION['flash_message'] = [
                    'type' => 'success',
                    'message' => __('login_success')
                ];
                
                // Redirect based on role
                $redirectUrl = match($_SESSION['user_role']) {
                    'admin' => '/admin/dashboard.php',
                    'vendor' => '/vendor/dashboard.php',
                    default => $_GET['redirect'] ?? '/'
                };
                
                header('Location: ' . $redirectUrl);
                exit;
            } else {
                $error = $result['error'];
            }
        }
    }
}

$pageTitle = 'Login'; // Default title
$breadcrumbs = [
    ['title' => 'Login']
];

require_once __DIR__ . '/includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-5 col-md-7">
            <div class="card shadow">
                <div class="card-body p-5">
                    <!-- Header -->
                    <div class="text-center mb-4">
                        <h2 class="fw-bold text-primary"><?= __('login') ?></h2>
                        <p class="text-muted"><?= __('welcome_back') ?></p>
                    </div>
                    
                    <!-- Error/Success Messages -->
                    <?php if ($error): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= Security::escape($error) ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= Security::escape($success) ?>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Login Form -->
                    <form method="POST" id="loginForm">
                        <?= Security::csrfField() ?>
                        
                        <!-- Email -->
                        <div class="mb-3">
                            <label for="email" class="form-label"><?= __('email') ?></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= Security::escape($_POST['email'] ?? '') ?>" 
                                       placeholder="<?= __('email') ?>" required>
                            </div>
                        </div>
                        
                        <!-- Password -->
                        <div class="mb-3">
                            <label for="password" class="form-label"><?= __('password') ?></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="<?= __('password') ?>" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Remember Me & Forgot Password -->
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    <?= __('remember_me') ?>
                                </label>
                            </div>
                            <a href="/forgot-password.php" class="text-decoration-none">
                                <?= __('forgot_password') ?>
                            </a>
                        </div>
                        
                        <!-- Submit Button -->
                        <button type="submit" class="btn btn-primary w-100 mb-3" id="loginBtn">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            <?= __('login') ?>
                        </button>
                    </form>
                    
                    <!-- Social Login (Optional) -->
                    <div class="text-center mb-3">
                        <span class="text-muted"><?= __('or_login_with') ?></span>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <button class="btn btn-outline-primary w-100">
                                <i class="fab fa-google me-2"></i>Google
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-outline-primary w-100">
                                <i class="fab fa-facebook-f me-2"></i>Facebook
                            </button>
                        </div>
                    </div>
                    
                    <!-- Register Link -->
                    <div class="text-center mt-4">
                        <p class="mb-0">
                            <?= __('dont_have_account') ?>
                            <a href="/register.php" class="text-decoration-none fw-bold">
                                <?= __('register') ?>
                            </a>
                        </p>
                    </div>
                    
                    <!-- Vendor Registration -->
                    <div class="text-center mt-2">
                        <p class="mb-0">
                            <?= __('want_to_sell') ?>
                            <a href="/register.php?role=vendor" class="text-decoration-none fw-bold text-success">
                                <?= __('become_vendor') ?>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Toggle password visibility
    $('#togglePassword').click(function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Form validation
    $('#loginForm').on('submit', function(e) {
        const email = $('#email').val().trim();
        const password = $('#password').val();
        
        if (!email || !password) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: window.ProVendor.translations.error,
                text: '<?= __('field_required') ?>'
            });
            return false;
        }
        
        if (!isValidEmail(email)) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: window.ProVendor.translations.error,
                text: '<?= __('invalid_email') ?>'
            });
            return false;
        }
        
        // Show loading state
        const btn = $('#loginBtn');
        const originalText = btn.html();
        btn.html('<span class="spinner"></span> ' + window.ProVendor.translations.loading);
        btn.prop('disabled', true);
        
        // Re-enable button after 5 seconds (in case of server issues)
        setTimeout(function() {
            btn.html(originalText);
            btn.prop('disabled', false);
        }, 5000);
    });
    
    // Email validation function
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // Auto-focus on first empty field
    if (!$('#email').val()) {
        $('#email').focus();
    } else if (!$('#password').val()) {
        $('#password').focus();
    }
});
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
