/* ProVendor - Professional E-commerce Platform */

:root {
    /* Modern Color Palette */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;

    /* Neutral Colors */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    --white: #ffffff;
    --black: #000000;

    /* Typography */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
    --font-family-mono: 'JetBrains Mono', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, Courier, monospace;

    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;

    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease;
    --transition: 200ms ease;
    --transition-slow: 300ms ease;

    /* Layout */
    --container-max-width: 1280px;
    --header-height: 80px;
    --nav-height: 60px;
}

/* Base Styles & Reset */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
}

body {
    font-family: var(--font-family-sans);
    font-size: 16px;
    line-height: 1.6;
    color: var(--gray-900);
    background-color: var(--gray-50);
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography Scale */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }

/* Font Weights */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }

/* Text Colors */
.text-gray-400 { color: var(--gray-400); }
.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-900 { color: var(--gray-900); }
.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-danger { color: var(--danger-color); }

/* RTL Support */
.rtl {
    direction: rtl;
    text-align: right;
}

.rtl body {
    font-family: 'Cairo', var(--font-family-sans);
}

/* Modern Header Design */
.header-top {
    background: var(--gray-900);
    color: var(--white);
    padding: var(--space-2) 0;
    font-size: var(--text-sm);
    border-bottom: 1px solid var(--gray-800);
}

.header-top a {
    color: var(--gray-300);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.header-top a:hover {
    color: var(--white);
}

.main-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-4) 0;
    position: sticky;
    top: 0;
    z-index: 50;
    box-shadow: var(--shadow-sm);
}

.logo {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--primary-color);
    text-decoration: none;
    letter-spacing: -0.025em;
}

.logo:hover {
    color: var(--primary-hover);
}

/* Modern Search Bar */
.search-form {
    position: relative;
    max-width: 500px;
    width: 100%;
}

.search-form .form-control {
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-base);
    transition: all var(--transition-fast);
    background: var(--white);
    box-shadow: var(--shadow-sm);
}

.search-form .form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.search-form .btn {
    position: absolute;
    right: var(--space-2);
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-2) var(--space-4);
    color: var(--white);
    transition: background-color var(--transition-fast);
}

.search-form .btn:hover {
    background: var(--primary-hover);
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.header-action-btn {
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: transparent;
    border: none;
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    font-size: var(--text-sm);
    font-weight: 500;
}

.header-action-btn:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.header-action-btn .badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: var(--danger-color);
    color: var(--white);
    font-size: 0.625rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-menu {
    position: relative;
}

.user-menu-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: var(--gray-100);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    color: var(--gray-700);
    text-decoration: none;
    transition: all var(--transition-fast);
    font-weight: 500;
}

.user-menu-btn:hover {
    background: var(--gray-200);
    border-color: var(--gray-300);
}

/* Modern Navigation */
.main-nav {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: 0;
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.nav-pills {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin: 0;
    padding: 0;
    list-style: none;
}

.nav-pills .nav-item {
    position: relative;
}

.nav-pills .nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4) var(--space-5);
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--text-sm);
    border-radius: 0;
    transition: all var(--transition-fast);
    position: relative;
}

.nav-pills .nav-link:hover {
    color: var(--primary-color);
    background: var(--primary-light);
}

.nav-pills .nav-link.active {
    color: var(--primary-color);
}

.nav-pills .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
}

/* Dropdown Menus */
.dropdown-menu {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--space-2);
    margin-top: var(--space-2);
    min-width: 200px;
}

.dropdown-item {
    display: block;
    padding: var(--space-2) var(--space-3);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    font-size: var(--text-sm);
}

.dropdown-item:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

/* Modern Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);
    font-weight: 600;
    line-height: 1;
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    user-select: none;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px var(--primary-light);
}

/* Button Variants */
.btn-primary {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
    color: var(--white);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-900);
    border-color: var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-200);
    border-color: var(--gray-400);
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
}

.btn-lg {
    padding: var(--space-4) var(--space-6);
    font-size: var(--text-base);
}

.btn-xl {
    padding: var(--space-5) var(--space-8);
    font-size: var(--text-lg);
}

/* Form Controls */
.form-control {
    display: block;
    width: 100%;
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-base);
    line-height: 1.5;
    color: var(--gray-900);
    background: var(--white);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

/* Modern Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-light) 100%);
    padding: var(--space-20) 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>') repeat;
    opacity: 0.5;
}

.hero-content {
    position: relative;
    z-index: 2;
}

/* Utility Classes */
.bg-gradient-to-r {
    background: linear-gradient(to right, var(--gray-50), var(--primary-light));
}

.from-blue-50 { background: var(--gray-50); }
.to-indigo-100 { background: var(--primary-light); }

.py-20 { padding-top: var(--space-20); padding-bottom: var(--space-20); }
.py-16 { padding-top: var(--space-16); padding-bottom: var(--space-16); }
.mb-12 { margin-bottom: var(--space-12); }
.mb-8 { margin-bottom: var(--space-8); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-2 { margin-bottom: var(--space-2); }
.mt-8 { margin-top: var(--space-8); }
.ms-2 { margin-left: var(--space-2); }
.me-2 { margin-right: var(--space-2); }

.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

.rounded-full { border-radius: var(--radius-full); }
.rounded-2xl { border-radius: var(--radius-2xl); }

.leading-tight { line-height: 1.25; }
.leading-relaxed { line-height: 1.625; }

/* Grid System */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }

/* Flexbox */
.flex { display: flex; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-content-center { justify-content: center; }
.flex-shrink-0 { flex-shrink: 0; }
.flex-grow-1 { flex-grow: 1; }

/* Display */
.inline-block { display: inline-block; }
.d-none { display: none; }

/* Responsive Display */
@media (min-width: 768px) {
    .d-md-inline { display: inline; }
    .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .md\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
    .d-lg-block { display: block; }
    .d-lg-inline { display: inline; }
    .lg\\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
    .mx-8 { margin-left: var(--space-8); margin-right: var(--space-8); }
}

/* Modern Layout System */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.main-layout {
    background: var(--white);
    min-height: calc(100vh - var(--header-height) - var(--nav-height));
}

/* Modern Product Grid */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-6);
    padding: var(--space-6) 0;
}

.product-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: all var(--transition);
    position: relative;
    box-shadow: var(--shadow-sm);
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--gray-300);
}

.product-image {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    background: var(--gray-100);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: var(--space-3);
    left: var(--space-3);
    background: var(--danger-color);
    color: var(--white);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    z-index: 10;
}

.product-actions {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    opacity: 0;
    transform: translateX(10px);
    transition: all var(--transition);
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
}

.product-action-btn {
    width: 40px;
    height: 40px;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.product-action-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: scale(1.1);
}

/* Product Card Content */
.product-info {
    padding: var(--space-5);
}

.product-vendor {
    font-size: var(--text-xs);
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-2);
    font-weight: 500;
}

.product-title {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--gray-900);
    line-height: 1.4;
    margin-bottom: var(--space-3);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-title a {
    color: inherit;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.product-title a:hover {
    color: var(--primary-color);
}

.product-rating {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
}

.rating-stars {
    display: flex;
    gap: 1px;
}

.rating-stars .star {
    color: #fbbf24;
    font-size: var(--text-sm);
}

.rating-stars .star.empty {
    color: var(--gray-300);
}

.rating-count {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

.product-price {
    display: flex;
    align-items: baseline;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
}

.price-current {
    font-size: var(--text-lg);
    font-weight: 700;
    color: var(--gray-900);
}

.price-original {
    font-size: var(--text-base);
    color: var(--gray-500);
    text-decoration: line-through;
}

.price-discount {
    font-size: var(--text-sm);
    color: var(--success-color);
    font-weight: 600;
    background: var(--success-color);
    color: var(--white);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius);
}

.add-to-cart-btn {
    width: 100%;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: 600;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.add-to-cart-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.add-to-cart-btn:active {
    transform: translateY(0);
}

/* Product Cards */
.product-card {
    position: relative;
    margin-bottom: 2rem;
}

.product-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    z-index: 10;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.product-badge.sale {
    background: var(--danger-color);
    color: var(--white);
}

.product-badge.new {
    background: var(--success-color);
    color: var(--white);
}

.product-badge.featured {
    background: var(--warning-color);
    color: var(--dark-color);
}

.product-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--transition);
}

.product-card:hover .product-actions {
    opacity: 1;
}

.product-action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: var(--white);
    color: var(--dark-color);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.product-action-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

/* Card Body */
.card-body {
    padding: 1.25rem;
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
    line-height: 1.4;
}

.card-text {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
    line-height: 1.5;
}

/* Price Display */
.price {
    font-weight: 700;
    color: var(--dark-color);
    font-size: 1.1rem;
}

.price-old {
    text-decoration: line-through;
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-right: 0.5rem;
}

.price-sale {
    color: var(--danger-color);
    font-weight: 700;
}

.price-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    text-transform: uppercase;
    font-size: 0.875rem;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(33, 150, 243, 0.3);
}

.btn-outline-primary {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Hero Section - Minago Style */
.hero-banner {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    padding: 2rem;
    margin: 1.5rem;
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
    min-height: 300px;
    display: flex;
    align-items: center;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-discount {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
    color: var(--dark-color);
}

.hero-title .highlight {
    color: var(--primary-color);
}

.hero-cta {
    background: var(--primary-color);
    color: var(--white);
    padding: 0.75rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
    margin-top: 1rem;
}

.hero-cta:hover {
    background: var(--primary-dark);
    color: var(--white);
    transform: translateY(-1px);
}

.hero-image {
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
}

/* Promotional Cards - Minago Style */
.promo-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid #e0e0e0;
    height: 100%;
}

.promo-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.promo-card .promo-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: var(--white);
    font-size: 1.2rem;
}

.promo-card h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
    font-size: 1rem;
}

.promo-card p {
    color: var(--secondary-color);
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.promo-card small {
    font-size: 0.75rem;
    font-weight: 600;
}

/* Featured Section */
.featured-section h3 {
    color: var(--dark-color);
    font-size: 1.5rem;
    font-weight: 700;
}

/* Category Grid */
.category-card {
    text-align: center;
    padding: 2rem 1rem;
    border-radius: var(--border-radius);
    background: var(--white);
    transition: var(--transition);
    text-decoration: none;
    color: var(--dark-color);
    display: block;
}

.category-card:hover {
    color: var(--primary-color);
    transform: translateY(-5px);
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer h5 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.footer a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 1rem;
    margin-top: 2rem;
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Section Styling */
.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.section-subtitle {
    color: var(--secondary-color);
    font-size: 1.1rem;
    margin-bottom: 3rem;
}

.bg-light-custom {
    background: #f8f9fa !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-layout {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
        padding: 1rem 0;
    }

    .sidebar-menu {
        display: flex;
        overflow-x: auto;
        gap: 1rem;
        padding: 0 1rem;
    }

    .sidebar-menu li {
        border: none;
        white-space: nowrap;
    }

    .hero-banner {
        margin: 1rem;
        padding: 1.5rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
    }

    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        padding: 1rem;
    }

    .nav-pills .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }

    .promo-card {
        margin-bottom: 1rem;
    }
}

/* Additional Enhancements */
.container-fluid {
    padding-left: 0;
    padding-right: 0;
}

.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus states */
.btn:focus,
.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
}

/* Loading animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Modern Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: var(--space-12) 0;
        text-align: center;
    }

    .text-5xl {
        font-size: 2.5rem;
    }

    .text-xl {
        font-size: 1.125rem;
    }

    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-4);
    }

    .nav-pills {
        flex-wrap: wrap;
        gap: var(--space-1);
    }

    .nav-pills .nav-link {
        padding: var(--space-2) var(--space-3);
        font-size: var(--text-xs);
    }

    .header-actions {
        gap: var(--space-2);
    }

    .btn-lg {
        padding: var(--space-3) var(--space-5);
        font-size: var(--text-sm);
    }

    .grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .container {
        padding: 0 var(--space-3);
    }

    .flex-wrap {
        flex-direction: column;
        gap: var(--space-3);
    }

    .mx-8 {
        margin-left: var(--space-4);
        margin-right: var(--space-4);
    }
}

@media (max-width: 480px) {
    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-3);
    }

    .text-5xl {
        font-size: 2rem;
    }

    .py-20 {
        padding-top: var(--space-10);
        padding-bottom: var(--space-10);
    }

    .py-16 {
        padding-top: var(--space-8);
        padding-bottom: var(--space-8);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #1e293b;
        --gray-100: #334155;
        --white: #0f172a;
        --gray-900: #f8fafc;
    }
}
