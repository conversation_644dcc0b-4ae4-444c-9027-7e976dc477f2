/* ProVendor - Modern E-commerce Styles */

:root {
    --primary-color: #2196F3;
    --primary-dark: #1976D2;
    --secondary-color: #6c757d;
    --success-color: #4CAF50;
    --danger-color: #F44336;
    --warning-color: #FF9800;
    --info-color: #00BCD4;
    --light-color: #f8f9fa;
    --dark-color: #212121;
    --white: #ffffff;
    --border-radius: 6px;
    --box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
    --nav-height: 60px;
}

/* RTL Support */
.rtl {
    direction: rtl;
    text-align: right;
}

.rtl .me-1, .rtl .me-2, .rtl .me-3 {
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
}

.rtl .ms-1, .rtl .ms-2, .rtl .ms-3 {
    margin-left: 0 !important;
    margin-right: 0.25rem !important;
}

/* Typography */
body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

.rtl body {
    font-family: 'Cairo', sans-serif;
}

/* Header Styles */
.top-bar {
    font-size: 0.875rem;
    background: var(--primary-dark) !important;
}

.main-header {
    position: sticky;
    top: 0;
    z-index: 1020;
    background: var(--white) !important;
    border-bottom: 1px solid #e0e0e0;
    height: var(--nav-height);
}

.logo-img {
    transition: var(--transition);
}

.logo:hover .logo-img {
    transform: scale(1.05);
}

.search-form .form-control {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    padding: 0.75rem 1rem;
}

.search-form .btn {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    padding: 0.75rem 1rem;
}

/* Language Switcher */
.language-switcher {
    display: flex;
    gap: 0.5rem;
}

.lang-link {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: var(--transition);
    font-size: 0.875rem;
}

.lang-link:hover,
.lang-link.active {
    color: var(--white);
    background: rgba(255,255,255,0.2);
}

/* Navigation */
.main-nav {
    background: var(--primary-color) !important;
    border: none;
    padding: 0;
}

.nav-pills .nav-link {
    color: var(--white) !important;
    border-radius: 0;
    margin: 0;
    padding: 1rem 1.5rem;
    transition: var(--transition);
    text-transform: uppercase;
    font-weight: 500;
    font-size: 0.9rem;
    border-right: 1px solid rgba(255,255,255,0.1);
}

.nav-pills .nav-link:hover,
.nav-pills .nav-link.active {
    background-color: var(--primary-dark) !important;
    color: var(--white) !important;
}

/* Cards */
.card {
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: var(--transition);
    overflow: hidden;
    background: var(--white);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #d0d0d0;
}

.card-img-top {
    height: 220px;
    object-fit: cover;
    transition: var(--transition);
    background: #f8f9fa;
}

.card:hover .card-img-top {
    transform: scale(1.02);
}

/* Product Cards */
.product-card {
    position: relative;
    margin-bottom: 2rem;
}

.product-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    z-index: 10;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.product-badge.sale {
    background: var(--danger-color);
    color: var(--white);
}

.product-badge.new {
    background: var(--success-color);
    color: var(--white);
}

.product-badge.featured {
    background: var(--warning-color);
    color: var(--dark-color);
}

.product-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--transition);
}

.product-card:hover .product-actions {
    opacity: 1;
}

.product-action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: var(--white);
    color: var(--dark-color);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.product-action-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

/* Card Body */
.card-body {
    padding: 1.25rem;
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
    line-height: 1.4;
}

.card-text {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
    line-height: 1.5;
}

/* Price Display */
.price {
    font-weight: 700;
    color: var(--dark-color);
    font-size: 1.1rem;
}

.price-old {
    text-decoration: line-through;
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-right: 0.5rem;
}

.price-sale {
    color: var(--danger-color);
    font-weight: 700;
}

.price-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    text-transform: uppercase;
    font-size: 0.875rem;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(33, 150, 243, 0.3);
}

.btn-outline-primary {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: var(--dark-color);
    padding: 3rem 0;
    position: relative;
    overflow: hidden;
    min-height: 400px;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.8;
}

.hero-cta {
    background: var(--primary-color);
    color: var(--white);
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
}

.hero-cta:hover {
    background: var(--primary-dark);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

/* Promotional Cards */
.promo-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid #e0e0e0;
}

.promo-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.promo-card .promo-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: var(--white);
    font-size: 1.5rem;
}

.promo-card h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.promo-card p {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin: 0;
}

/* Category Grid */
.category-card {
    text-align: center;
    padding: 2rem 1rem;
    border-radius: var(--border-radius);
    background: var(--white);
    transition: var(--transition);
    text-decoration: none;
    color: var(--dark-color);
    display: block;
}

.category-card:hover {
    color: var(--primary-color);
    transform: translateY(-5px);
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer h5 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.footer a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 1rem;
    margin-top: 2rem;
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Section Styling */
.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.section-subtitle {
    color: var(--secondary-color);
    font-size: 1.1rem;
    margin-bottom: 3rem;
}

.bg-light-custom {
    background: #f8f9fa !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 0;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .product-actions {
        opacity: 1;
    }

    .card-img-top {
        height: 200px;
    }

    .nav-pills .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }

    .promo-card {
        margin-bottom: 1rem;
    }
}

/* Additional Enhancements */
.container-fluid {
    padding-left: 0;
    padding-right: 0;
}

.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus states */
.btn:focus,
.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
}

/* Loading animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #2c3e50;
        --white: #34495e;
        --dark-color: #ecf0f1;
    }
}
