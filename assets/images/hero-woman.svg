<svg width="450" height="400" viewBox="0 0 450 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f0f8ff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#e6f3ff;stop-opacity:0.3" />
    </linearGradient>
    <linearGradient id="skin" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fdbcb4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f4a6a0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="hair" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b4513;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a0522d;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dress" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b9d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff8fab;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bag1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4fc3f7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#29b6f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bag2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#81c784;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#66bb6a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bag3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffb74d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffa726;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background -->
  <rect width="450" height="400" fill="url(#bg)"/>

  <!-- Woman Figure -->
  <!-- Head -->
  <circle cx="225" cy="100" r="30" fill="url(#skin)"/>

  <!-- Hair (curly) -->
  <path d="M 195 85 Q 225 65 255 85 Q 260 95 255 115 Q 250 125 240 130 Q 230 135 225 135 Q 220 135 210 130 Q 200 125 195 115 Q 190 95 195 85" fill="url(#hair)"/>
  <!-- Hair curls -->
  <circle cx="200" cy="90" r="8" fill="url(#hair)"/>
  <circle cx="250" cy="90" r="8" fill="url(#hair)"/>
  <circle cx="210" cy="75" r="6" fill="url(#hair)"/>
  <circle cx="240" cy="75" r="6" fill="url(#hair)"/>

  <!-- Face features -->
  <circle cx="215" cy="95" r="2" fill="#333"/>
  <circle cx="235" cy="95" r="2" fill="#333"/>
  <path d="M 220 105 Q 225 110 230 105" stroke="#333" stroke-width="2" fill="none"/>

  <!-- Body -->
  <ellipse cx="225" cy="180" rx="25" ry="50" fill="url(#dress)"/>

  <!-- Arms -->
  <ellipse cx="190" cy="160" rx="12" ry="35" fill="url(#skin)" transform="rotate(-20 190 160)"/>
  <ellipse cx="260" cy="160" rx="12" ry="35" fill="url(#skin)" transform="rotate(20 260 160)"/>

  <!-- Legs -->
  <rect x="210" y="230" width="12" height="40" rx="6" fill="url(#skin)"/>
  <rect x="228" y="230" width="12" height="40" rx="6" fill="url(#skin)"/>

  <!-- Shoes -->
  <ellipse cx="216" cy="275" rx="8" ry="5" fill="#333"/>
  <ellipse cx="234" cy="275" rx="8" ry="5" fill="#333"/>

  <!-- Shopping Bags -->
  <!-- Left hand bag (Blue) -->
  <rect x="120" y="200" width="40" height="50" rx="3" fill="url(#bag1)"/>
  <rect x="125" y="195" width="30" height="8" rx="4" fill="#42a5f5"/>
  <circle cx="135" cy="200" r="2" fill="#1e88e5"/>
  <circle cx="145" cy="200" r="2" fill="#1e88e5"/>
  <line x1="175" y1="180" x2="140" y2="195" stroke="#1e88e5" stroke-width="3"/>

  <!-- Right hand bag (Green) -->
  <rect x="290" y="210" width="35" height="45" rx="3" fill="url(#bag2)"/>
  <rect x="295" y="205" width="25" height="8" rx="4" fill="#4caf50"/>
  <circle cx="302" cy="210" r="2" fill="#388e3c"/>
  <circle cx="318" cy="210" r="2" fill="#388e3c"/>
  <line x1="275" y1="180" x2="307" y2="205" stroke="#388e3c" stroke-width="3"/>

  <!-- Additional bag (Orange) -->
  <rect x="340" y="220" width="30" height="40" rx="3" fill="url(#bag3)"/>
  <rect x="345" y="215" width="20" height="6" rx="3" fill="#ff9800"/>
  <circle cx="350" cy="220" r="1.5" fill="#f57c00"/>
  <circle cx="360" cy="220" r="1.5" fill="#f57c00"/>
  <line x1="275" y1="185" x2="355" y2="215" stroke="#f57c00" stroke-width="2"/>

  <!-- Decorative shopping elements -->
  <circle cx="80" cy="120" r="6" fill="#e91e63" opacity="0.7"/>
  <circle cx="370" cy="140" r="8" fill="#9c27b0" opacity="0.6"/>
  <circle cx="390" cy="100" r="5" fill="#ff5722" opacity="0.8"/>
  <circle cx="60" cy="180" r="7" fill="#00bcd4" opacity="0.5"/>

  <!-- Shopping icons -->
  <g transform="translate(50, 50)">
    <rect width="20" height="15" rx="2" fill="#ff4081" opacity="0.6"/>
    <text x="10" y="10" text-anchor="middle" font-family="Arial" font-size="8" fill="white">%</text>
  </g>

  <g transform="translate(350, 60)">
    <circle r="12" fill="#4caf50" opacity="0.6"/>
    <text x="0" y="4" text-anchor="middle" font-family="Arial" font-size="10" fill="white">$</text>
  </g>
</svg>
